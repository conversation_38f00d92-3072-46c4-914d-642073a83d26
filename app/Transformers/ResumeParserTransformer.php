<?php

namespace App\Transformers;

class ResumeParserTransformer
{
    public static function transform($data, $text = null)
    {
        return [
            'fullName' => $data['candidate_name'] ?? null,
            'phoneNumber' => $data['phone_number'] ?? null,
            'email' => $data['email'] ?? null,
            'skills' => $data['skills'] ?? null,
            'totalExpYears' => $data['total_experience_years'] ?? null,
            'totalExpMonths' => $data['total_experience_months'] ?? null,
            'currentCompany' => $data['current_company'] ?? $data['current_org'] ?? null,
            'designation' => $data['designation'] ?? null,
            'currentLocation' => $data['current_location'] ?? null,
            'noticePeriod' => $data['notice_period'] ?? null,
            'qualification' => $data['qualification'] ?? null,
            'preferredLocation' => $data['preferred_location'] ?? null,
            'currentSalaryLacs' => $data['current_salary_lacs'] ?? $data['current_ctc'] ?? null,
            'currentSalaryThousands' => $data['current_salary_thousands'] ?? null,
            'expectedSalaryLacs' => $data['expected_salary_lacs'] ?? $data['expected_ctc'] ?? null,
            'expectedSalaryThousands' => $data['expected_salary_thousands'] ?? null,
            'relevantExpYears' => $data['relevant_experience_years'] ?? null,
            'relevantExpMonths' => $data['relevant_experience_months'] ?? null,
            'homeTown' => $data['home_town'] ?? null,
            'resumeContent' => $text ?? null,
        ];
    }
}
