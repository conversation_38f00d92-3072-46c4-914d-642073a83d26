<?php

namespace App\Services;

use App\Models\ArsRanking;
use App\Models\JobDescription;
use App\Models\Resume;
use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ArsService
{
    protected $openaiApiKey;

    public function __construct()
    {
        $this->openaiApiKey = config('services.openai.api_key');
    }

    /**
     * Rank a resume against a job description
     *
     * @param Resume $resume
     * @param JobDescription $jobDescription
     * @param User $user
     * @return ArsRanking|null
     */
    public function rankResume(Resume $resume, JobDescription $jobDescription, User $user)
    {
        try {
            // Check if user has enough points
            if ($user->points < $user->ars_points_deduction) {
                return [
                    'success' => false,
                    'message' => 'You do not have enough credits to use the ARS feature.'
                ];
            }

            // Get resume content
            $resumeContent = $resume->resume_content;
            if (!$resumeContent) {
                // If resume_content is empty, construct a comprehensive resume from available fields
                $resumeContent = $this->constructResumeContent($resume);

                // If we still don't have any content after construction, return an error
                if (empty(trim($resumeContent))) {
                    return [
                        'success' => false,
                        'message' => 'Not enough resume information available for ranking.'
                    ];
                }
            }

            // Get job description content
            $jdContent = $jobDescription->description;
            if (!$jdContent) {
                return [
                    'success' => false,
                    'message' => 'Job description content is not available for ranking.'
                ];
            }

            // Limit content to 3000 characters
            $resumeContent = Str::limit($resumeContent, 3000);
            $jdContent = Str::limit($jdContent, 3000);

            // Call OpenAI API to rank the resume
            $result = $this->callOpenAI($resumeContent, $jdContent);

            if (!$result['success']) {
                return $result;
            }

            // Deduct points from user
            $user->points -= $user->ars_points_deduction;
            $user->save();

            // Create or update ARS ranking
            $arsRanking = ArsRanking::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'resume_id' => $resume->id,
                    'job_description_id' => $jobDescription->id,
                ],
                [
                    'score' => $result['score'],
                    'feedback' => $result['feedback'],
                ]
            );

            return [
                'success' => true,
                'ranking' => $arsRanking,
                'message' => 'Resume ranked successfully.'
            ];
        } catch (\Exception $e) {
            Log::error('ARS ranking error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while ranking the resume.'
            ];
        }
    }

    /**
     * Construct a comprehensive resume content from available resume fields
     *
     * @param Resume $resume
     * @return string
     */
    private function constructResumeContent(Resume $resume)
    {
        $content = [];

        // Personal Information
        if ($resume->candidate_name) {
            $content[] = "Name: " . $resume->candidate_name;
        }

        if ($resume->email) {
            $content[] = "Email: " . $resume->email;
        }

        if ($resume->phone_number) {
            $phoneDisplay = $resume->country_code ? $resume->country_code . ' ' . $resume->phone_number : $resume->phone_number;
            $content[] = "Phone: " . $phoneDisplay;
        }

        if ($resume->current_location) {
            $content[] = "Current Location: " . $resume->current_location;
        }

        // Professional Summary
        $professionalSummary = [];

        if ($resume->designation) {
            $professionalSummary[] = $resume->designation;
        }

        if ($resume->total_experience_years) {
            $experienceText = $resume->total_experience_years . " years";
            if ($resume->relevant_experience_years) {
                $experienceText .= " (with " . $resume->relevant_experience_years . " years of relevant experience)";
            }
            $professionalSummary[] = $experienceText . " of experience";
        }

        if ($resume->current_company) {
            $professionalSummary[] = "Currently working at " . $resume->current_company;
        }

        if (!empty($professionalSummary)) {
            $content[] = "Professional Summary: " . implode(". ", $professionalSummary) . ".";
        }

        // Skills
        if ($resume->skills && is_array($resume->skills) && !empty($resume->skills)) {
            $content[] = "Skills: " . implode(", ", $resume->skills);
        }

        // Education
        if ($resume->qualification) {
            $content[] = "Education: " . $resume->qualification;
        }

        // Employment Details
        $employmentDetails = [];

        if ($resume->current_company) {
            $employmentDetails[] = "Current/Last Company: " . $resume->current_company;
        }

        if ($resume->designation) {
            $employmentDetails[] = "Role: " . $resume->designation;
        }

        if (!empty($employmentDetails)) {
            $content[] = "Employment: " . implode(", ", $employmentDetails);
        }

        // Additional Information
        $additionalInfo = [];

        if ($resume->notice_period) {
            $additionalInfo[] = "Notice Period: " . $resume->notice_period;
        }

        if ($resume->current_salary) {
            $additionalInfo[] = "Current Salary: " . $resume->current_salary;
        }

        if ($resume->expected_salary) {
            $additionalInfo[] = "Expected Salary: " . $resume->expected_salary;
        }

        if ($resume->preferred_location) {
            $additionalInfo[] = "Preferred Location: " . $resume->preferred_location;
        }

        if ($resume->home_town) {
            $additionalInfo[] = "Home Town: " . $resume->home_town;
        }

        if (!empty($additionalInfo)) {
            $content[] = "Additional Information: " . implode(", ", $additionalInfo);
        }

        // Comments
        if ($resume->comments) {
            $content[] = "Comments: " . $resume->comments;
        }

        return implode("\n", $content);
    }

    /**
     * Call OpenAI API to rank the resume
     *
     * @param string $resumeContent
     * @param string $jdContent
     * @return array
     */
    private function callOpenAI(string $resumeContent, string $jdContent)
    {
        try {
            $prompt = "You are an expert HR professional tasked with evaluating how well a candidate's resume matches a job description.

            Please analyze the following resume and job description, then provide:
            1. A score from 1 to 10 (where 10 is a perfect match)
            2. A brief explanation of your rating (maximum 500 words) with pointers

            Focus on skills match, experience relevance, and overall fit.

            JOB DESCRIPTION:
            {$jdContent}

            RESUME:
            {$resumeContent}

            Respond with a JSON object containing:
            {
                \"score\": [number between 1-10],
                \"feedback\": [explanation text]
            }";

            $requestBody = [
                'model' => 'gpt-4-turbo-2024-04-09',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'response_format' => [
                    'type' => 'json_object'
                ]
            ];

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->openaiApiKey
            ])
                ->timeout(60)
                ->post(
                    "https://api.openai.com/v1/chat/completions",
                    $requestBody
                );

            if ($response->successful()) {
                $responseText = $response->json()['choices'][0]['message']['content'];

                // Clean and parse the response using the helper method
                $parsedData = $this->cleanAndParseJsonResponse($responseText);

                if ($parsedData !== null && isset($parsedData['score']) && isset($parsedData['feedback'])) {
                    return [
                        'success' => true,
                        'score' => $parsedData['score'],
                        'feedback' => $parsedData['feedback']
                    ];
                }

                Log::error('Failed to parse OpenAI response after cleaning attempts');
                return [
                    'success' => false,
                    'message' => 'Failed to parse OpenAI response.'
                ];
            }

            Log::error('OpenAI API error: ' . $response->body());
            return [
                'success' => false,
                'message' => 'Failed to get response from OpenAI.'
            ];
        } catch (\Exception $e) {
            Log::error('OpenAI API exception: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred while calling OpenAI API.'
            ];
        }
    }

    /**
     * Clean and parse JSON response from ChatGPT API
     * Handles UTF-8 encoding issues and markdown formatting
     *
     * @param string $responseText
     * @return array|null
     */
    private function cleanAndParseJsonResponse($responseText)
    {
        try {
            // Log the original response for debugging
            Log::info('Original OpenAI response: ' . substr($responseText, 0, 500));

            // Step 1: Clean UTF-8 encoding issues
            $responseText = mb_convert_encoding($responseText, 'UTF-8', 'UTF-8');
            $responseText = preg_replace('/[\x00-\x1F\x7F]/', '', $responseText); // Remove control characters

            // Step 2: Remove various markdown formatting patterns
            $patterns = [
                '/```json\s*/i',           // Opening ```json
                '/```\s*$/i',              // Closing ```
                '/^```.*?\n/m',            // Any line starting with ```
                '/\n```.*?$/m',            // Any line ending with ```
                '/^json\s*/i',             // Line starting with "json"
                '/^\s*json\s*{/i',         // "json {" at start
            ];

            foreach ($patterns as $pattern) {
                $responseText = preg_replace($pattern, '', $responseText);
            }

            // Step 3: Trim whitespace and newlines
            $responseText = trim($responseText);

            // Step 4: Try to find JSON object if response contains extra text
            if (!str_starts_with($responseText, '{')) {
                // Look for JSON object pattern
                if (preg_match('/\{.*\}/s', $responseText, $matches)) {
                    $responseText = $matches[0];
                }
            }

            // Step 5: Additional cleaning for common issues
            $responseText = str_replace(['"json":', 'json:', '```'], '', $responseText);
            $responseText = trim($responseText);

            // Log the cleaned response
            Log::info('Cleaned OpenAI response: ' . substr($responseText, 0, 500));

            // Step 6: Attempt to decode JSON
            $parsedData = json_decode($responseText, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                return $parsedData;
            }

            // Step 7: If JSON decode failed, try additional cleaning
            Log::warning('First JSON decode failed: ' . json_last_error_msg());

            // Try to fix common JSON issues
            $responseText = $this->fixCommonJsonIssues($responseText);
            $parsedData = json_decode($responseText, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                return $parsedData;
            }

            // Log the final error
            Log::error('JSON decode error after all cleaning attempts: ' . json_last_error_msg());
            Log::error('Final cleaned response: ' . $responseText);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception in cleanAndParseJsonResponse: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Fix common JSON formatting issues
     *
     * @param string $jsonString
     * @return string
     */
    private function fixCommonJsonIssues($jsonString)
    {
        // Fix unescaped quotes in values
        $jsonString = preg_replace('/(?<!\\\\)"(?![,}\]\s])/', '\\"', $jsonString);

        // Fix trailing commas
        $jsonString = preg_replace('/,(\s*[}\]])/', '$1', $jsonString);

        // Fix missing quotes around keys
        $jsonString = preg_replace('/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/', '$1"$2":', $jsonString);

        // Fix single quotes to double quotes
        $jsonString = str_replace("'", '"', $jsonString);

        return $jsonString;
    }
}
