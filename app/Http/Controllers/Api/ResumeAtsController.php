<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Resume;
use App\Models\ResumeAtsScore;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use PhpOffice\PhpWord\IOFactory;
use Smalot\PdfParser\Parser;



class ResumeAtsController extends Controller
{
    /**
     * Save resume and create ATS score record
     */
    public function saveResumeWithAts(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'resume_file' => 'required|file|mimes:pdf,doc,docx',
            'candidate_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:40',
            'email' => 'required|email|max:255',
            'skills' => 'required',
            'total_experience_years' => 'nullable|max:100',
            'total_experience_months' => 'nullable|max:100',
            'current_company' => 'nullable|string|max:255',
            'designation' => 'nullable|string|max:255',
            'current_location' => 'nullable|string|max:255',
            'qualification' => 'nullable|string|max:255',
            'current_salary_lacs' => 'nullable',
            'current_salary_thousands' => 'nullable',
            'notice_period' => 'nullable|max:100',
            'partner_name' => 'nullable|string|max:255',
            'comments' => 'nullable|string|max:1000',
            'submitted_from' => 'nullable|string|max:255',
            'partner_id' => 'nullable|exists:users,id',
            'partner_email' => 'nullable|email|max:255',
            'partner_phone_number' => 'nullable|string|max:40',
        ]);

        if ($validate->fails()) {
            return response()->json(['error' => $validate->errors()->first()], 400);
        }

        try {
            $data = $request->except(['resume_file', 'partner_email', 'partner_phone_number']);

            // Extract text from uploaded file for resume_content
            $file = $request->file('resume_file');
            $extension = $file->getClientOriginalExtension();
            $resumeContent = $this->extractTextFromFile($file, $extension);
            $data['resume_content'] = $resumeContent;

            // Handle skills
            $data['skills'] = is_array($data['skills']) ? $data['skills'] : explode(',', $data['skills']);

            // Store file
            $data['resume_file'] = $request->file('resume_file')->store('resumes', 's3', 'public');
            $formattedfileName = $request->candidate_name . '_' . ($request->designation ?? 'Resume') . '.' .
                $request->file('resume_file')->getClientOriginalExtension();
            $data['resume_file_name'] = $formattedfileName;

            if ($request->submitted_from != 'partner') {
                $data['partner_name'] = $request->submitted_from == 'resume_writing' ? 'Resume Writing' : 'Job Seeker';
            }

            // Handle partner creation if needed
            $newPartnerId = null;
            if ($request->submitted_from == 'partner' && $request->partner_email) {
                $existingPartner = User::where('email', $request->partner_email)->exists();
                if (!$existingPartner) {
                    $partner = User::create([
                        'name' => $request->partner_name,
                        'email' => $request->partner_email,
                        'phone_number' => $request->partner_phone_number,
                        'role' => 'executive',
                        'executive_role' => 'Partner',
                        'password' => Hash::make(Str::random(8)),
                    ]);
                    $newPartnerId = $partner->id;
                }
            }

            // Check for existing resume by email
            $existingResume = Resume::where('email', $data['email'])->first();

            if ($existingResume) {
                $existingResume->update($data);
                $resume = $existingResume;
            } else {
                $resume = Resume::create($data);
            }

            if ($newPartnerId) {
                $resume->update(['partner_id' => $newPartnerId]);
            }

            // Create ATS Score record with UUID
            $atsScore = ResumeAtsScore::create([
                'resume_id' => $resume->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Resume saved successfully',
                'resume_id' => $resume->id,
                'ats_uuid' => $atsScore->id
            ]);
        } catch (\Exception $e) {
            Log::error('Resume save error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save resume'], 500);
        }
    }

    /**
     * Parse ATS score for a resume by UUID
     */
    public function parseAtsScore(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'ats_uuid' => 'required|uuid|exists:resume_ats_scores,id',
        ]);

        if ($validate->fails()) {
            return response()->json(['error' => $validate->errors()->first()], 400);
        }

        try {
            $atsScore = ResumeAtsScore::with('resume')->find($request->ats_uuid);

            if (!$atsScore) {
                return response()->json(['error' => 'ATS score record not found'], 404);
            }

            // If already parsed, return existing data
            if ($atsScore->isParsed()) {
                return response()->json([
                    'success' => true,
                    'message' => 'ATS score already parsed',
                    'data' => [
                        'uuid' => $atsScore->id,
                        'resume_score' => $atsScore->resume_score,
                        'ats_friendly_status' => $atsScore->ats_friendly_status,
                        'resume_feedback' => $atsScore->resume_feedback,
                        'suggestions_for_improvement' => $atsScore->suggestions_for_improvement,
                    ]
                ]);
            }

            // Parse using ChatGPT
            $parseResult = $this->parseWithChatGPT($atsScore->resume);

            if (!$parseResult['success']) {
                return response()->json(['error' => $parseResult['message']], 500);
            }

            // Update ATS score with parsed data
            $atsScore->update([
                'resume_score' => $parseResult['data']['resume_score'],
                'ats_friendly_status' => $parseResult['data']['ats_friendly_status'],
                'resume_feedback' => $parseResult['data']['resume_feedback'],
                'suggestions_for_improvement' => $parseResult['data']['suggestions_for_improvement'],
                'status' => 'parsed',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'ATS score parsed successfully',
                'data' => [
                    'uuid' => $atsScore->id,
                    'resume_score' => $atsScore->resume_score,
                    'ats_friendly_status' => $atsScore->ats_friendly_status,
                    'resume_feedback' => $atsScore->resume_feedback,
                    'suggestions_for_improvement' => $atsScore->suggestions_for_improvement,
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('ATS parsing error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to parse ATS score'], 500);
        }
    }

    /**
     * Extract text from uploaded file
     */
    public function extractTextFromFile($file, $extension)
    {
        if ($extension == 'pdf') {
            return $this->extractFromPdf($file);
        }

        return $this->extractFromDoc($file);
    }

    public function extractFromPdf($file)
    {
        try {
            $parser = new Parser();
            $pdf = $parser->parseFile($file->getPathname());
            $text = $pdf->getText();

            // Check if text was successfully extracted
            if (empty(trim($text))) {
                Log::warning('Empty text extracted from PDF: ' . $file->getClientOriginalName());
            }

            return $text;
        } catch (\Exception $e) {
            Log::error('PDF parsing error: ' . $e->getMessage());
            return '';
        }
    }

    public function extractFromDoc($file)
    {
        try {
            // Load the document with error handling for corrupted images
            $phpWord = IOFactory::load($file->getPathname());
            $text = '';

            // Extract main document content
            foreach ($phpWord->getSections() as $section) {
                // Extract headers with error handling
                try {
                    foreach ($section->getHeaders() as $header) {
                        foreach ($header->getElements() as $element) {
                            $text .= $this->extractElementText($element) . ' ';
                        }
                        $text .= "\n";
                    }
                } catch (\Exception $e) {
                    Log::warning('Error processing headers: ' . $e->getMessage());
                }

                // Extract section content with error handling
                try {
                    foreach ($section->getElements() as $element) {
                        $text .= $this->extractElementText($element) . ' ';
                    }
                } catch (\Exception $e) {
                    Log::warning('Error processing section elements: ' . $e->getMessage());
                }

                // Extract footers with error handling
                try {
                    foreach ($section->getFooters() as $footer) {
                        foreach ($footer->getElements() as $element) {
                            $text .= $this->extractElementText($element) . ' ';
                        }
                        $text .= "\n";
                    }
                } catch (\Exception $e) {
                    Log::warning('Error processing footers: ' . $e->getMessage());
                }

                $text .= "\n"; // Add line break after each section
            }

            return trim($text);
        } catch (\Exception $e) {
            Log::error('DOCX parsing error: ' . $e->getMessage());

            // If the error is related to invalid images, try to extract text without images
            if (strpos($e->getMessage(), 'Invalid image') !== false) {
                Log::info('Attempting to extract text while skipping corrupted images');
                try {
                    // Try alternative approach - load with different settings
                    $phpWord = IOFactory::load($file->getPathname());
                    return $this->extractTextSafely($phpWord);
                } catch (\Exception $fallbackException) {
                    Log::error('Fallback DOCX parsing also failed: ' . $fallbackException->getMessage());
                }
            }

            return '';
        }
    }

    /**
     * Helper method to extract text from different element types
     *
     * @param mixed $element
     * @return string
     */
    private function extractElementText($element)
    {
        $text = '';

        try {
            if ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                foreach ($element->getElements() as $textElement) {
                    try {
                        if ($textElement instanceof \PhpOffice\PhpWord\Element\Text) {
                            $text .= $textElement->getText() . ' ';
                        } elseif ($textElement instanceof \PhpOffice\PhpWord\Element\PreserveText) {
                            $text .= $textElement->getText() . ' ';
                        } elseif ($textElement instanceof \PhpOffice\PhpWord\Element\Link) {
                            $text .= $textElement->getText() . ' ';
                        } elseif ($textElement instanceof \PhpOffice\PhpWord\Element\Image) {
                            // Skip images gracefully - they don't contain extractable text
                            Log::info('Skipping image element during text extraction');
                            continue;
                        }
                    } catch (\Exception $e) {
                        Log::warning('Error processing text element: ' . $e->getMessage());
                        continue;
                    }
                }
                $text .= "\n";
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Text) {
                $text .= $element->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Table) {
                foreach ($element->getRows() as $row) {
                    foreach ($row->getCells() as $cell) {
                        foreach ($cell->getElements() as $cellElement) {
                            $text .= $this->extractElementText($cellElement) . ' ';
                        }
                    }
                    $text .= "\n";
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Link) {
                $text .= $element->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\ListItem) {
                $text .= $element->getTextObject()->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Footnote) {
                foreach ($element->getElements() as $footnoteElement) {
                    $text .= $this->extractElementText($footnoteElement) . ' ';
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Image) {
                // Skip images gracefully - they don't contain extractable text
                Log::info('Skipping image element during text extraction');
                return '';
            }
        } catch (\Exception $e) {
            Log::warning('Error processing element: ' . $e->getMessage());
            return '';
        }

        return $text;
    }


    /**
     * Safely extract text from PhpWord document, skipping problematic elements
     *
     * @param \PhpOffice\PhpWord\PhpWord $phpWord
     * @return string
     */
    private function extractTextSafely($phpWord)
    {
        $text = '';

        try {
            foreach ($phpWord->getSections() as $section) {
                foreach ($section->getElements() as $element) {
                    try {
                        $text .= $this->extractElementText($element) . ' ';
                    } catch (\Exception $e) {
                        Log::warning('Skipping problematic element: ' . $e->getMessage());
                        continue;
                    }
                }
                $text .= "\n";
            }
        } catch (\Exception $e) {
            Log::error('Safe text extraction failed: ' . $e->getMessage());
        }

        return trim($text);
    }

    /**
     * Parse resume data using ChatGPT for ATS scoring
     */
    private function parseWithChatGPT(Resume $resume)
    {
        try {
            $openaiApiKey = config('services.openai.api_key');

            if (!$openaiApiKey) {
                return [
                    'success' => false,
                    'message' => 'OpenAI API key not configured'
                ];
            }

            // Prepare resume data for analysis
            $resumeData = [
                'candidate_name' => $resume->candidate_name,
                'phone_number' => $resume->phone_number,
                'email' => $resume->email,
                'skills' => is_array($resume->skills) ? implode(', ', $resume->skills) : $resume->skills,
                'total_experience_years' => $resume->total_experience_years,
                'total_experience_months' => $resume->total_experience_months,
                'current_company' => $resume->current_company,
                'designation' => $resume->designation,
                'current_location' => $resume->current_location,
                'qualification' => $resume->qualification,
                'current_salary_lacs' => $resume->current_salary_lacs,
                'current_salary_thousands' => $resume->current_salary_thousands,
                'notice_period' => $resume->notice_period,
                'resume_content' => Str::limit($resume->resume_content, 3000),
            ];

            $prompt = $this->buildAtsPrompt($resumeData);

            $requestBody = [
                'model' => 'gpt-4-turbo-2024-04-09',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'response_format' => [
                    'type' => 'json_object'
                ]
            ];

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $openaiApiKey
            ])
                ->timeout(60)
                ->post("https://api.openai.com/v1/chat/completions", $requestBody);

            if ($response->successful()) {
                $responseText = $response->json()['choices'][0]['message']['content'];
                $parsedData = $this->cleanAndParseJsonResponse($responseText);

                if ($parsedData !== null && $this->validateAtsResponse($parsedData)) {
                    return [
                        'success' => true,
                        'data' => $parsedData
                    ];
                }

                Log::error('Failed to parse ChatGPT ATS response');
                return [
                    'success' => false,
                    'message' => 'Failed to parse ChatGPT response'
                ];
            }

            Log::error('ChatGPT API request failed: ' . $response->body());
            return [
                'success' => false,
                'message' => 'ChatGPT API request failed'
            ];
        } catch (\Exception $e) {
            Log::error('ChatGPT parsing error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'ChatGPT parsing failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Build the ChatGPT prompt for ATS scoring
     */
    private function buildAtsPrompt(array $resumeData)
    {
        $resumeText = "RESUME DATA:\n";
        foreach ($resumeData as $key => $value) {
            if (!empty($value)) {
                $resumeText .= ucfirst(str_replace('_', ' ', $key)) . ": " . $value . "\n";
            }
        }

        return "You are an expert ATS (Applicant Tracking System) analyzer and HR professional.

Analyze the following resume data and provide a comprehensive ATS score and feedback.

{$resumeText}

Please provide:
1. A resume score from 1 to 100 (where 100 is perfect for ATS systems)
2. ATS-friendly status: 'excellent' (90-100), 'good' (70-89), 'fair' (50-69), or 'poor' (below 50)
3. Detailed feedback about the resume's ATS compatibility
4. Specific suggestions for improvement to make it more ATS-friendly

Focus on:
- Keyword optimization and relevance
- Format and structure compatibility with ATS
- Contact information completeness
- Skills presentation
- Experience description clarity
- Education formatting
- Overall ATS readability

Respond with a JSON object containing:
{
    \"resume_score\": [number between 1-100],
    \"ats_friendly_status\": [\"excellent\"|\"good\"|\"fair\"|\"poor\"],
    \"resume_feedback\": [detailed feedback text about ATS compatibility],
    \"suggestions_for_improvement\": [specific actionable suggestions to improve ATS score]
}";
    }

    /**
     * Clean and parse JSON response from ChatGPT
     */
    private function cleanAndParseJsonResponse($responseText)
    {
        try {
            // Remove markdown code blocks if present
            $responseText = preg_replace('/```json\s*/', '', $responseText);
            $responseText = preg_replace('/```\s*$/', '', $responseText);
            $responseText = trim($responseText);

            // Try to decode JSON
            $decoded = json_decode($responseText, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                return $decoded;
            }

            // If direct decode fails, try to extract JSON from the response
            if (preg_match('/\{.*\}/s', $responseText, $matches)) {
                $jsonString = $matches[0];
                $decoded = json_decode($jsonString, true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    return $decoded;
                }
            }

            Log::error('JSON parsing failed for response: ' . $responseText);
            return null;
        } catch (\Exception $e) {
            Log::error('Exception in JSON parsing: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate the ATS response structure
     */
    private function validateAtsResponse($data)
    {
        return isset($data['resume_score']) &&
            isset($data['ats_friendly_status']) &&
            isset($data['resume_feedback']) &&
            isset($data['suggestions_for_improvement']) &&
            is_numeric($data['resume_score']) &&
            in_array($data['ats_friendly_status'], ['excellent', 'good', 'fair', 'poor']);
    }
}
