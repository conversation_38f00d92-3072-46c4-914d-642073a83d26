<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\City;
use App\Models\Resume;
use App\Models\User;
use App\Transformers\ResumeParserTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use PhpOffice\PhpWord\IOFactory;
use Smalot\PdfParser\Parser;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ResumeParserController extends Controller
{
    private $openaiApiKey;

    public function __construct()
    {
        $this->openaiApiKey = config('services.openai.api_key');
    }

    public function parse(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'resume_file' => 'required|file|mimes:pdf,doc,docx'
        ]);

        if ($validate->fails()) {
            return response()->json(['error' => 'Invalid file format'], 400);
        }

        try {
            $file = $request->file('resume_file');
            $extension = $file->getClientOriginalExtension();

            // Generate unique file hash based on content
            $fileContent = file_get_contents($file->getRealPath());
            $fileHash = md5($fileContent);

            // Check if we have cached results for this file
            $cacheKey = 'resume_parse_' . $fileHash;
            if (Cache::has($cacheKey)) {
                return response()->json(Cache::get($cacheKey));
            }

            // Extract text from file
            $text = $this->extractTextFromFile($file, $extension);

            // Check for email in the text
            $emailPattern = '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/';
            if (preg_match($emailPattern, $text, $matches)) {
                $email = $matches[0];
                $threeMonthsAgo = now()->subMonths(3);
                $existingResume = Resume::where('email', $email)
                    ->whereNotNull('submitted_from')
                    ->where('created_at', '>=', $threeMonthsAgo)
                    ->first();

                if ($existingResume) {
                    $allowedDate = $existingResume->created_at->addMonths(3)->format('F j, Y');
                    return response()->json([
                        'error' => "This profile was recently uploaded or updated within the last three months and cannot be submitted again at this time. Please try again after {$allowedDate}. If you believe this is an error, contact our support team for <NAME_EMAIL>"
                    ], 400);
                }
            }

            // Parse using ChatGPT API - passing text
            $parsedData = $this->parseTextWithChatGPT($text, $file);
            if (!$parsedData) {
                return response()->json(['error' => 'The Resume File could not be uploaded because it appears to be corrupted or unreadable. Please try re-uploading the file in a supported format (e.g., PDF, DOC, or DOCX). If the issue persists, please check the file for errors or create a new file and upload it again.
If you still need help, please contact our support <NAME_EMAIL>'], 500);
            }

            // Cache the parsed data for 3 months
            Cache::put($cacheKey, $parsedData, now()->addMonths(3));

            return response()->json($parsedData);
        } catch (\Exception $e) {
            // Log detailed error information for debugging
            Log::error('Resume parsing error: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            // Check if it's a JSON encoding error specifically
            if (strpos($e->getMessage(), 'json_encode') !== false || strpos($e->getMessage(), 'Malformed UTF-8') !== false) {
                Log::error('JSON encoding error detected - likely UTF-8 or markdown formatting issue');
            }

            return response()->json(['error' => 'The Resume File could not be uploaded because it appears to be corrupted or unreadable. Please try re-uploading the file in a supported format (e.g., PDF, DOC, or DOCX). If the issue persists, please check the file for errors or create a new file and upload it again.
If you still need help, please contact our support <NAME_EMAIL>
'], 500);
        }
    }

    public function extractTextFromFile($file, $extension)
    {
        if ($extension == 'pdf') {
            return $this->extractFromPdf($file);
        }

        return $this->extractFromDoc($file);
    }

    public function extractFromPdf($file)
    {
        try {
            $parser = new Parser();
            $pdf = $parser->parseFile($file->getPathname());
            $text = $pdf->getText();

            // Check if text was successfully extracted
            if (empty(trim($text))) {
                Log::warning('Empty text extracted from PDF: ' . $file->getClientOriginalName());
            }

            return $text;
        } catch (\Exception $e) {
            Log::error('PDF parsing error: ' . $e->getMessage());
            return '';
        }
    }

    public function extractFromDoc($file)
    {
        try {
            // Load the document with error handling for corrupted images
            $phpWord = IOFactory::load($file->getPathname());
            $text = '';

            // Extract main document content
            foreach ($phpWord->getSections() as $section) {
                // Extract headers with error handling
                try {
                    foreach ($section->getHeaders() as $header) {
                        foreach ($header->getElements() as $element) {
                            $text .= $this->extractElementText($element) . ' ';
                        }
                        $text .= "\n";
                    }
                } catch (\Exception $e) {
                    Log::warning('Error processing headers: ' . $e->getMessage());
                }

                // Extract section content with error handling
                try {
                    foreach ($section->getElements() as $element) {
                        $text .= $this->extractElementText($element) . ' ';
                    }
                } catch (\Exception $e) {
                    Log::warning('Error processing section elements: ' . $e->getMessage());
                }

                // Extract footers with error handling
                try {
                    foreach ($section->getFooters() as $footer) {
                        foreach ($footer->getElements() as $element) {
                            $text .= $this->extractElementText($element) . ' ';
                        }
                        $text .= "\n";
                    }
                } catch (\Exception $e) {
                    Log::warning('Error processing footers: ' . $e->getMessage());
                }

                $text .= "\n"; // Add line break after each section
            }

            return trim($text);
        } catch (\Exception $e) {
            Log::error('DOCX parsing error: ' . $e->getMessage());

            // If the error is related to invalid images, try to extract text without images
            if (strpos($e->getMessage(), 'Invalid image') !== false) {
                Log::info('Attempting to extract text while skipping corrupted images');
                try {
                    // Try alternative approach - load with different settings
                    $phpWord = IOFactory::load($file->getPathname());
                    return $this->extractTextSafely($phpWord);
                } catch (\Exception $fallbackException) {
                    Log::error('Fallback DOCX parsing also failed: ' . $fallbackException->getMessage());
                }
            }

            return '';
        }
    }

    /**
     * Safely extract text from PhpWord document, skipping problematic elements
     *
     * @param \PhpOffice\PhpWord\PhpWord $phpWord
     * @return string
     */
    private function extractTextSafely($phpWord)
    {
        $text = '';

        try {
            foreach ($phpWord->getSections() as $section) {
                foreach ($section->getElements() as $element) {
                    try {
                        $text .= $this->extractElementText($element) . ' ';
                    } catch (\Exception $e) {
                        Log::warning('Skipping problematic element: ' . $e->getMessage());
                        continue;
                    }
                }
                $text .= "\n";
            }
        } catch (\Exception $e) {
            Log::error('Safe text extraction failed: ' . $e->getMessage());
        }

        return trim($text);
    }

    /**
     * Helper method to extract text from different element types
     *
     * @param mixed $element
     * @return string
     */
    private function extractElementText($element)
    {
        $text = '';

        try {
            if ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                foreach ($element->getElements() as $textElement) {
                    try {
                        if ($textElement instanceof \PhpOffice\PhpWord\Element\Text) {
                            $text .= $textElement->getText() . ' ';
                        } elseif ($textElement instanceof \PhpOffice\PhpWord\Element\PreserveText) {
                            $text .= $textElement->getText() . ' ';
                        } elseif ($textElement instanceof \PhpOffice\PhpWord\Element\Link) {
                            $text .= $textElement->getText() . ' ';
                        } elseif ($textElement instanceof \PhpOffice\PhpWord\Element\Image) {
                            // Skip images gracefully - they don't contain extractable text
                            Log::info('Skipping image element during text extraction');
                            continue;
                        }
                    } catch (\Exception $e) {
                        Log::warning('Error processing text element: ' . $e->getMessage());
                        continue;
                    }
                }
                $text .= "\n";
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Text) {
                $text .= $element->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Table) {
                foreach ($element->getRows() as $row) {
                    foreach ($row->getCells() as $cell) {
                        foreach ($cell->getElements() as $cellElement) {
                            $text .= $this->extractElementText($cellElement) . ' ';
                        }
                    }
                    $text .= "\n";
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Link) {
                $text .= $element->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\ListItem) {
                $text .= $element->getTextObject()->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Footnote) {
                foreach ($element->getElements() as $footnoteElement) {
                    $text .= $this->extractElementText($footnoteElement) . ' ';
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Image) {
                // Skip images gracefully - they don't contain extractable text
                Log::info('Skipping image element during text extraction');
                return '';
            }
        } catch (\Exception $e) {
            Log::warning('Error processing element: ' . $e->getMessage());
            return '';
        }

        return $text;
    }

    public function parseTextWithChatGPT($text, $file = null) // $file parameter kept for compatibility
    {
        $prompt =
            'Act as a professional resume parser. Extract structured information from the provided resume text and return a clean JSON object with exactly these keys:
{
 "candidate_name": "Full name of the candidate (Title Case or null)",
 "phone_number": "Phone number or null",
 "email": "Email address (lowercase or null)",
 "skills": [List of all skills, including technical and soft skills, mentioned anywhere in the resume or empty array],
 "total_experience_years": "Total years of experience (numeric or null)",
 "current_company": "Current employer name or null",
 "designation": "Current job title or null",
 "current_location": "Current city or location or null",
 "qualification": "Highest degree and specialization or null"
}

Guidelines:
•⁠  ⁠Output only the JSON object with no explanation, no markdown, no code blocks.
•⁠  ⁠If a field is missing or not explicitly mentioned, return null.
•⁠  ⁠candidate_name should be Title Case.
•⁠  ⁠email should be lowercase.
•⁠  ⁠skills should be an array of individual strings.
•⁠  ⁠Do not guess or infer missing information. Use only explicit text.

Resume Text (3000 chars):
' . Str::limit($text, 3000);

        $requestBody = [
            'model' => 'gpt-4-turbo-2024-04-09',
            'store' => true,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'response_format' => [
                'type' => 'json_object'
            ]
        ];

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->openaiApiKey
        ])
            ->timeout(60)
            ->post(
                "https://api.openai.com/v1/chat/completions",
                $requestBody
            );

        if ($response->successful()) {
            try {
                $responseText = $response->json()['choices'][0]['message']['content'];

                // Clean and parse the response using the new helper method
                $parsedData = $this->cleanAndParseJsonResponse($responseText);

                if ($parsedData !== null) {
                    // Convert skills to array if it's a string
                    if (isset($parsedData['skills']) && is_string($parsedData['skills'])) {
                        $parsedData['skills'] = array_map('trim', explode(',', $parsedData['skills']));
                    }

                    return ResumeParserTransformer::transform($parsedData, $text);
                }

                Log::error('Failed to parse ChatGPT response after cleaning attempts');
            } catch (\Exception $e) {
                Log::error('ChatGPT parsing error: ' . $e->getMessage());
            }
        }

        Log::error('ChatGPT API error: ' . $response->body());
        return null;
    }

    public function parseTextWithChatGPTSystem($text, $file = null) // $file parameter kept for compatibility
    {
        $prompt =
            'Act as a professional resume parser. Extract structured information from the provided resume text and return a clean JSON object with exactly these keys:
{
 "candidate_name": "Full name of the candidate (Title Case or null)",
 "phone_number": "Phone number or null",
 "email": "Email address (lowercase or null)",
 "skills": [List of all skills, including technical and soft skills, mentioned anywhere in the resume or empty array],
 "total_experience_years": "Total years of experience (numeric or null)",
 "current_company": "Current employer name or null",
 "designation": "Current job title or null",
 "current_location": "Current city or location or null",
 "qualification": "Highest degree and specialization or null"
}

Guidelines:
•⁠  ⁠Output only the JSON object with no explanation, no markdown, no code blocks.
•⁠  ⁠If a field is missing or not explicitly mentioned, return null.
•⁠  ⁠candidate_name should be Title Case.
•⁠  ⁠email should be lowercase.
•⁠  ⁠skills should be an array of individual strings.
•⁠  ⁠Do not guess or infer missing information. Use only explicit text.

Resume Text (3000 chars):
' . Str::limit($text, 3000);


        $requestBody = [
            'model' => 'gpt-4-turbo-2024-04-09',
            'store' => true,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'response_format' => [
                'type' => 'json_object'
            ]
        ];

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->openaiApiKey
        ])
            ->timeout(60)
            ->post(
                "https://api.openai.com/v1/chat/completions",
                $requestBody
            );

        if ($response->successful()) {
            try {
                $responseText = $response->json()['choices'][0]['message']['content'];

                // Clean and parse the response using the new helper method
                $parsedData = $this->cleanAndParseJsonResponse($responseText);

                if ($parsedData !== null) {
                    // Convert skills to array if it's a string
                    if (isset($parsedData['skills']) && is_string($parsedData['skills'])) {
                        $parsedData['skills'] = array_map('trim', explode(',', $parsedData['skills']));
                    }

                    return $parsedData;
                }

                Log::error('Failed to parse ChatGPT response after cleaning attempts');
            } catch (\Exception $e) {
                Log::error('ChatGPT parsing error: ' . $e->getMessage());
            }
        }

        Log::error('ChatGPT API error: ' . $response->body());
        return null;
    }


    public function saveResume(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'resume_file' => 'required|file|mimes:pdf,doc,docx',
            'candidate_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:40',
            'email' => 'required|email|max:255',
            'skills' => 'required',
            'total_experience_years' => 'nullable|max:100',
            'total_experience_months' => 'nullable|max:100',
            'relevant_experience_years' => 'nullable|max:100',
            'relevant_experience_months' => 'nullable|max:100',
            'current_company' => 'nullable|string|max:255',
            'current_location' => 'nullable|string|max:255',
            'home_town' => 'nullable|string|max:255',
            'current_salary_lacs' => 'nullable',
            'current_salary_thousands' => 'nullable',
            'expected_salary_lacs' => 'nullable',
            'expected_salary_thousands' => 'nullable',
            'notice_period' => 'nullable|max:100',
            'current_company' => 'nullable|string|max:255',
            'designation' => 'nullable|string|max:255',
            'qualification' => 'nullable|string|max:255',
            'partner_name' => 'nullable|string|max:255',
            'comments' => 'nullable|string|max:1000',
            'resume_content' => 'nullable|string|max:50000',
            'submitted_from' => 'nullable|string|max:255',
            'partner_id' => 'nullable|exists:users,id',
            'partner_email' => 'nullable|email|max:255',
            'partner_phone_number' => 'nullable|string|max:40',
        ]);

        if ($validate->fails()) {
            return response()->json(['error' => $validate->errors()->first()], 400);
        }


        $data = $validate->validated();


        // Check for email in the text
        $email = $data['email'];
        $threeMonthsAgo = now()->subMonths(3);
        $existingResume = Resume::where('email', $email)
            ->whereNotNull('submitted_from')
            ->where('created_at', '>=', $threeMonthsAgo)
            ->first();

        if ($existingResume) {
            $allowedDate = $existingResume->created_at->addMonths(3)->format('F j, Y');
            return response()->json([
                'error' => "This profile was recently uploaded or updated within the last three months and cannot be submitted again at this time. Please try again after {$allowedDate}. If you believe this is an error, contact our support team for <NAME_EMAIL>"
            ], 400);
        }



        $data['skills'] = is_array($data['skills']) ? $data['skills'] : explode(',', $data['skills']);
        $data['resume_file'] = $request->file('resume_file')->store('resumes', 's3', 'public');
        $formattedfileName = $request->candidate_name . '_' . $request->designation . '.' .
            $request->file('resume_file')->getClientOriginalExtension();
        $data['resume_file_name'] = $formattedfileName;

        if ($request->submitted_from != 'partner') {
            $data['partner_name'] = $request->submitted_from == 'resume_writing' ? 'Resume Writing' : 'Job Seeker';
        }

        $existingResume = Resume::where('email', $data['email'])->first();

        $newPartnerId = null;
        if ($request->submitted_from == 'partner' && $request->partner_email) {
            $existingPartner = User::where('email', $request->partner_email)->exists();
            if (!$existingPartner) {
                $partner = User::create([
                    'name' => $request->partner_name,
                    'email' => $request->partner_email,
                    'phone_number' => $request->partner_phone_number,
                    'role' => 'executive',
                    'executive_role' => 'Partner',
                    'password' => Hash::make(Str::random(8)),
                ]);
                $newPartnerId = $partner->id;
            }
        }


        if ($existingResume) {
            $existingResume->update($data);
            $resume = $existingResume;
        } else {
            $resume = Resume::create($data);
        }

        if ($newPartnerId) {
            $resume->update(['partner_id' => $newPartnerId]);
        }

        return response()->json(['message' => 'Resume saved successfully', 'resume' => $resume], 201);
    }

    public function validateEmailPhone(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'email' => 'nullable|email|max:255|unique:resumes,email',
            'phone_number' => 'nullable|string|max:40|unique:resumes,phone_number',
        ]);

        if ($validate->fails()) {
            return response()->json(['error' => $validate->errors()->first()], 400);
        }

        return response()->json(['message' => 'Validation successful'], 200);
    }

    public function getCities(Request $request)
    {

        $search = $request->input('search');

        $cities = City::where('name', 'like', '%' . $search . '%')
            ->orderByRaw(
                "CASE WHEN name = ? THEN 0 WHEN name LIKE ? THEN 1 ELSE 2 END",
                [$search, $search . '%']
            )
            ->limit(10)
            ->get();

        return response()->json(['cities' => $cities], 200);
    }

    public function getPartners(Request $request)
    {

        $search = $request->input('search');

        $partners = User::where('name', 'like', '%' . $search . '%')
            ->orderByRaw(
                "CASE WHEN name = ? THEN 0 WHEN name LIKE ? THEN 1 ELSE 2 END",
                [$search, $search . '%']
            )
            ->where('role', 'executive')
            ->where('executive_role', 'Partner')
            ->limit(10)
            ->get(['name', 'email', 'phone_number', 'id']);

        return response()->json(['partners' => $partners], 200);
    }

    /**
     * Clean and parse JSON response from ChatGPT API
     * Handles UTF-8 encoding issues and markdown formatting
     *
     * @param string $responseText
     * @return array|null
     */
    private function cleanAndParseJsonResponse($responseText)
    {
        try {
            // Log the original response for debugging
            // Log::info('Original ChatGPT response: ' . substr($responseText, 0, 500));

            // Step 1: Clean UTF-8 encoding issues
            $responseText = mb_convert_encoding($responseText, 'UTF-8', 'UTF-8');
            $responseText = preg_replace('/[\x00-\x1F\x7F]/', '', $responseText); // Remove control characters

            // Step 2: Remove various markdown formatting patterns
            $patterns = [
                '/```json\s*/i',           // Opening ```json
                '/```\s*$/i',              // Closing ```
                '/^```.*?\n/m',            // Any line starting with ```
                '/\n```.*?$/m',            // Any line ending with ```
                '/^json\s*/i',             // Line starting with "json"
                '/^\s*json\s*{/i',         // "json {" at start
            ];

            foreach ($patterns as $pattern) {
                $responseText = preg_replace($pattern, '', $responseText);
            }

            // Step 3: Trim whitespace and newlines
            $responseText = trim($responseText);

            // Step 4: Try to find JSON object if response contains extra text
            if (!str_starts_with($responseText, '{')) {
                // Look for JSON object pattern
                if (preg_match('/\{.*\}/s', $responseText, $matches)) {
                    $responseText = $matches[0];
                }
            }

            // Step 5: Additional cleaning for common issues
            $responseText = str_replace(['"json":', 'json:', '```'], '', $responseText);
            $responseText = trim($responseText);

            // Log the cleaned response
            // Log::info('Cleaned ChatGPT response: ' . substr($responseText, 0, 500));

            // Step 6: Attempt to decode JSON
            $parsedData = json_decode($responseText, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                return $parsedData;
            }

            // Step 7: If JSON decode failed, try additional cleaning
            Log::warning('First JSON decode failed: ' . json_last_error_msg());

            // Try to fix common JSON issues
            $responseText = $this->fixCommonJsonIssues($responseText);
            $parsedData = json_decode($responseText, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                return $parsedData;
            }

            // Log the final error
            Log::error('JSON decode error after all cleaning attempts: ' . json_last_error_msg());
            Log::error('Final cleaned response: ' . $responseText);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception in cleanAndParseJsonResponse: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Fix common JSON formatting issues
     *
     * @param string $jsonString
     * @return string
     */
    private function fixCommonJsonIssues($jsonString)
    {
        // Fix unescaped quotes in values
        $jsonString = preg_replace('/(?<!\\\\)"(?![,}\]\s])/', '\\"', $jsonString);

        // Fix trailing commas
        $jsonString = preg_replace('/,(\s*[}\]])/', '$1', $jsonString);

        // Fix missing quotes around keys
        $jsonString = preg_replace('/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/', '$1"$2":', $jsonString);

        // Fix single quotes to double quotes
        $jsonString = str_replace("'", '"', $jsonString);

        return $jsonString;
    }
}
