<?php

namespace App\Livewire\Traits;

use App\Models\Resume;

trait ResumeFilters
{
    /**
     * Apply all filters to the query (extracted for reusability)
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param bool $includeArchiveFilters Whether to include archive and category filters (default: true)
     */
    protected function applyFiltersToQuery($query, $includeArchiveFilters = true)
    {
        // Filter 1: Archive Status (only if includeArchiveFilters is true)
        if ($includeArchiveFilters && isset($this->resumeType)) {
            if ($this->resumeType === 'archived') {
                $query->where('is_archived', true);
            } elseif ($this->resumeType === 'not_archived') {
                $query->where('is_archived', false);
            }

            // Filter 2: Resume Category
            if (isset($this->resumeCategory)) {
                if ($this->resumeCategory === 'partner') {
                    $query->where('submitted_from', 'partner');
                } elseif ($this->resumeCategory === 'resume_writing') {
                    $query->where('submitted_from', 'resume_writing');
                } elseif ($this->resumeCategory === 'job_seeker') {
                    $query->where('submitted_from', 'job_seeker');
                } elseif ($this->resumeCategory === 'duplicates_email') {
                    // Optimized duplicate email detection using window function approach
                    $query->whereIn('email', function ($subQuery) {
                        $subQuery->select('email')
                            ->from('resumes')
                            ->whereNotNull('email')
                            ->where('email', '!=', '')
                            ->groupBy('email')
                            ->havingRaw('COUNT(*) > 1');
                    });
                } elseif ($this->resumeCategory === 'duplicates_phone') {
                    // Optimized duplicate phone detection using window function approach
                    $query->whereIn('phone_number', function ($subQuery) {
                        $subQuery->select('phone_number')
                            ->from('resumes')
                            ->whereNotNull('phone_number')
                            ->where('phone_number', '!=', '')
                            ->groupBy('phone_number')
                            ->havingRaw('COUNT(*) > 1');
                    });
                }
            }
        }

        // Apply date range filters
        if (isset($this->startDate) && $this->startDate) {
            $query->whereDate('created_at', '>=', $this->startDate);
        }
        if (isset($this->endDate) && $this->endDate) {
            $query->whereDate('created_at', '<=', $this->endDate);
        }

        if (isset($this->search) && $this->search) {
            $searchTerms = array_map('trim', explode(',', $this->search));

            $query->where(function ($q) use ($searchTerms) {
                foreach ($searchTerms as $term) {
                    if (empty($term)) continue;

                    $q->where(function ($subQuery) use ($term) {
                        // Use LIKE for better performance instead of REGEXP
                        // Add % wildcards for partial matching
                        $likeTerm = '%' . $term . '%';

                        $subQuery->where('candidate_name', 'LIKE', $likeTerm)
                            ->orWhere('email', 'LIKE', $likeTerm)
                            ->orWhere('phone_number', 'LIKE', $likeTerm)
                            ->orWhere('skills', 'LIKE', $likeTerm)
                            ->orWhere('current_location', 'LIKE', $likeTerm)
                            ->orWhere('preferred_location', 'LIKE', $likeTerm)
                            ->orWhere('qualification', 'LIKE', $likeTerm)
                            ->orWhere('total_experience_years', 'LIKE', $likeTerm)
                            ->orWhere('current_salary_lacs', 'LIKE', $likeTerm)
                            ->orWhere('expected_salary_lacs', 'LIKE', $likeTerm)
                            ->orWhere('notice_period', 'LIKE', $likeTerm)
                            ->orWhere('partner_name', 'LIKE', $likeTerm)
                            ->orWhere('home_town', 'LIKE', $likeTerm)
                            ->orWhere('relevant_experience_years', 'LIKE', $likeTerm)
                            ->orWhere('current_company', 'LIKE', $likeTerm)
                            ->orWhere('comments', 'LIKE', $likeTerm)
                            ->orWhere('designation', 'LIKE', $likeTerm);

                        // Only search resume_content if it's selected in the query
                        // This is determined by checking if the field is available
                        $columns = $subQuery->getQuery()->columns ?? ['*'];
                        if (in_array('*', $columns) || in_array('resume_content', $columns)) {
                            $subQuery->orWhere('resume_content', 'LIKE', $likeTerm);
                        }
                    });
                }
            });
        }


        // Experience filters
        if ((isset($this->minExperienceYears) && $this->minExperienceYears) ||
            (isset($this->minExperienceMonths) && $this->minExperienceMonths)) {
            $query->where(function ($q) {
                $minYears = (int)($this->minExperienceYears ?? 0);
                $minMonths = (int)($this->minExperienceMonths ?? 0);
                $q->where(function ($subQ) use ($minYears, $minMonths) {
                    $subQ->where(function ($innerQ) use ($minYears, $minMonths) {
                        $innerQ->where('total_experience_years', '>', $minYears)
                            ->orWhere(function ($deepQ) use ($minYears, $minMonths) {
                                $deepQ->where('total_experience_years', $minYears)
                                    ->where('total_experience_months', '>=', $minMonths);
                            });
                    })->orWhere('total_experience_years', '30+');
                });
            });
        }

        if ((isset($this->maxExperienceYears) && $this->maxExperienceYears) ||
            (isset($this->maxExperienceMonths) && $this->maxExperienceMonths)) {
            $query->where(function ($q) {
                $maxYears = (int)($this->maxExperienceYears ?? 0);
                $maxMonths = (int)($this->maxExperienceMonths ?? 0);
                if ($maxYears >= 30) {
                    $q->where(function ($subQ) use ($maxYears, $maxMonths) {
                        $subQ->where(function ($innerQ) use ($maxYears, $maxMonths) {
                            $innerQ->where('total_experience_years', '<', $maxYears)
                                ->orWhere(function ($deepQ) use ($maxYears, $maxMonths) {
                                    $deepQ->where('total_experience_years', $maxYears)
                                        ->where('total_experience_months', '<=', $maxMonths);
                                });
                        })->orWhere('total_experience_years', '30+');
                    });
                } else {
                    $q->where(function ($subQ) use ($maxYears, $maxMonths) {
                        $subQ->where('total_experience_years', '<', $maxYears)
                            ->orWhere(function ($innerQ) use ($maxYears, $maxMonths) {
                                $innerQ->where('total_experience_years', $maxYears)
                                    ->where('total_experience_months', '<=', $maxMonths);
                            });
                    });
                }
            });
        }

        // Salary filters
        if ((isset($this->minSalaryLacs) && $this->minSalaryLacs) ||
            (isset($this->minSalaryThousands) && $this->minSalaryThousands)) {
            $query->where(function ($q) {
                $minLacs = (int)($this->minSalaryLacs ?? 0);
                $minThousands = (int)($this->minSalaryThousands ?? 0);
                $q->where(function ($subQ) use ($minLacs, $minThousands) {
                    $subQ->where('expected_salary_lacs', '>', $minLacs)
                        ->orWhere(function ($innerQ) use ($minLacs, $minThousands) {
                            $innerQ->where('expected_salary_lacs', $minLacs)
                                ->where('expected_salary_thousands', '>=', $minThousands);
                        });
                });
            });
        }

        if ((isset($this->maxSalaryLacs) && $this->maxSalaryLacs) ||
            (isset($this->maxSalaryThousands) && $this->maxSalaryThousands)) {
            $query->where(function ($q) {
                $maxLacs = (int)($this->maxSalaryLacs ?? 0);
                $maxThousands = (int)($this->maxSalaryThousands ?? 0);
                $q->where(function ($subQ) use ($maxLacs, $maxThousands) {
                    $subQ->where('expected_salary_lacs', '<', $maxLacs)
                        ->orWhere(function ($innerQ) use ($maxLacs, $maxThousands) {
                            $innerQ->where('expected_salary_lacs', $maxLacs)
                                ->where('expected_salary_thousands', '<=', $maxThousands);
                        });
                });
            });
        }

        // Location filter
        if (isset($this->location) && $this->location) {
            $query->where('current_location', 'like', '%' . $this->location . '%');
        }

        // Notice period filter
        if (isset($this->noticePeriod) && $this->noticePeriod) {
            if ($this->noticePeriod == "immediate") {
                $query->where('notice_period', 0);
            } else {
                list($min, $max) = explode('-', $this->noticePeriod);
                $query->whereBetween('notice_period', [$min, $max]);
            }
        }
    }
}
