<?php

namespace App\Livewire;

use App\Models\Resume;
use App\Models\ResumeUnlock;
use App\Livewire\Traits\ResumeFilters;
use Flux\Flux;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;
use ZipArchive;


class SearchCandidates extends Component
{
    use WithPagination, WithFileUploads, ResumeFilters;

    public $search = '';
    public $minExperienceYears = null;
    public $minExperienceMonths = null;
    public $maxExperienceYears = null;
    public $maxExperienceMonths = null;
    public $minSalaryLacs = null;
    public $minSalaryThousands = null;
    public $maxSalaryLacs = null;
    public $maxSalaryThousands = null;
    public $location = '';
    public $noticePeriod = '';
    public $startDate = null;
    public $endDate = null;
    public $resumeType = 'all';
    public $resumeCategory = 'all';
    public $selectedResumes = [];
    public $selectedJobDescriptionId = null;

    public $csvFile;

    public $showCareerFluteResumes = true;


    public function mount()
    {
        $this->resetFilters();

        if (Auth::user()->role == 'admin') {
            $this->resumeType = 'not_archived';
            $this->resumeCategory = 'all';
        }

        // Check if there's a selected job description in the session
        if (session()->has('selected_job_description_id')) {
            $this->selectedJobDescriptionId = session('selected_job_description_id');
        }
    }

    public function handleJobDescriptionSelected($jobDescriptionId)
    {
        $this->selectedJobDescriptionId = $jobDescriptionId;
    }

    public function handleJobDescriptionCleared()
    {
        $this->selectedJobDescriptionId = null;
    }

    public function resetFilters()
    {
        $this->reset([
            'search',
            'minExperienceYears',
            'minExperienceMonths',
            'maxExperienceYears',
            'maxExperienceMonths',
            'minSalaryLacs',
            'minSalaryThousands',
            'maxSalaryLacs',
            'maxSalaryThousands',
            'location',
            'noticePeriod',
            'startDate',
            'endDate',
            'resumeType',
            'resumeCategory',
            'selectedResumes'
        ]);
    }



    public function archiveSelectedResumes()
    {
        if (empty($this->selectedResumes)) {
            return;
        }

        // If current filter is showing archived resumes, then unarchive the selected ones
        // Otherwise, archive the selected ones
        $setToArchived = $this->resumeType !== 'archived';

        Resume::whereIn('id', $this->selectedResumes)
            ->update(['is_archived' => $setToArchived]);

        $this->selectedResumes = [];
        $this->dispatch(
            'notify',
            message: ($setToArchived ? 'Archived' : 'Unarchived') . ' resumes successfully!',
            type: 'success'
        );
    }


    public function applyFilters()
    {
        $this->dispatch('filters-applied');
    }

    public function deleteResume($id)
    {
        $resume = Resume::findOrFail($id);

        if ($resume) {
            // Use soft delete (file is not deleted from storage)
            $resume->delete();

            $this->dispatch(
                'notify',
                message: 'Resume deleted successfully!',
                type: 'success'
            );
        }
    }

    /**
     * Permanently delete a resume and its file
     * This should only be used by admins when needed
     */
    public function forceDeleteResume($id)
    {
        $resume = Resume::withTrashed()->findOrFail($id);

        if ($resume) {
            if ($resume->resume_file) {
                Storage::disk('public')->delete($resume->resume_file);
            }
            $resume->forceDelete();

            $this->dispatch(
                'notify',
                message: 'Resume permanently deleted!',
                type: 'success'
            );
        }
    }


    public function updatedCsvFile()
    {
        $this->validate([
            'csvFile' => 'required|file|mimes:csv,txt|max:51200', // 50MB max
        ]);

        try {
            $path = $this->csvFile->getRealPath();
            $file = fopen($path, 'r');

            // Get headers from first row
            $headers = fgetcsv($file);
            $requiredHeaders = [
                'Partner Name',
                'Candidate Name',
                'Candidate Email ID',
                'Candidate Phone Number',
                'Education',
                'Current Location',
                'Home Town',
                'Role',
                'Skills',
                'Total Experience Years',
                'Total Experience Months',
                'Relevant Experience Years',
                'Relevant Experience Months',
                'Current/ Last Company',
                'Current/ Last CTC Lacs',
                'Current/ Last CTC Thousands',
                'Expected CTC Lacs',
                'Expected CTC Thousands',
                'Notice Period',
                'Comments'
            ];

            // Validate headers
            $missingHeaders = array_diff($requiredHeaders, $headers);
            if (!empty($missingHeaders)) {
                throw new \Exception('Missing required columns: ' . implode(', ', $missingHeaders));
            }

            // Process each row
            while (($row = fgetcsv($file)) !== false) {
                $data = array_combine($headers, $row);

                // Process skills (convert comma-separated string to array)
                $skills = array_map('trim', explode(',', $data['Skills']));

                // Get experience values directly from the CSV
                $totalExpYears = $data['Total Experience Years'];
                $totalExpMonths = $data['Total Experience Months'];

                // Handle 30+ years special case
                $totalExpYearsValue = $totalExpYears;
                if ((int)$totalExpYears >= 30) {
                    $totalExpYearsValue = '30+';
                }

                // Get relevant experience values directly from the CSV
                $relevantExpYears = $data['Relevant Experience Years'];
                $relevantExpMonths = $data['Relevant Experience Months'];

                // Handle 30+ years special case for relevant experience
                $relevantExpYearsValue = $relevantExpYears;
                if ((int)$relevantExpYears >= 30) {
                    $relevantExpYearsValue = '30+';
                }

                // Get salary values directly from the CSV
                $currentSalaryLacs = $data['Current/ Last CTC Lacs'];
                $currentSalaryThousands = $data['Current/ Last CTC Thousands'];

                $expectedSalaryLacs = $data['Expected CTC Lacs'];
                $expectedSalaryThousands = $data['Expected CTC Thousands'];

                // Create resume record
                Resume::create([
                    'partner_name' => $data['Partner Name'],
                    'candidate_name' => $data['Candidate Name'],
                    'phone_number' => $data['Candidate Phone Number'],
                    'email' => $data['Candidate Email ID'],
                    'skills' => $skills,
                    'total_experience_years' => $totalExpYearsValue,
                    'total_experience_months' => $totalExpMonths,
                    'relevant_experience_years' => $relevantExpYearsValue,
                    'relevant_experience_months' => $relevantExpMonths,
                    'current_company' => $data['Current/ Last Company'],
                    'designation' => $data['Role'],
                    'current_location' => $data['Current Location'],
                    'notice_period' => $data['Notice Period'],
                    'qualification' => $data['Education'],
                    'current_salary_lacs' => $currentSalaryLacs,
                    'current_salary_thousands' => $currentSalaryThousands,
                    'expected_salary_lacs' => $expectedSalaryLacs,
                    'expected_salary_thousands' => $expectedSalaryThousands,
                    'comments' => $data['Comments'],
                    'home_town' => $data['Home Town'],
                    'is_archived' => false,
                ]);
            }

            fclose($file);

            $this->reset('csvFile');
            $this->dispatch(
                'notify',
                message: 'CSV imported successfully',
                type: 'success'
            );
            $this->dispatch('csv-imported');
        } catch (\Exception $e) {
            $this->dispatch(
                'notify',
                message: 'Error importing CSV: ' . $e->getMessage(),
                type: 'error'
            );
            $this->reset('csvFile');
        }
    }

    public function downloadTemplate()
    {
        $headers = [
            'Partner Name',
            'Candidate Name',
            'Candidate Email ID',
            'Candidate Phone Number',
            'Education',
            'Current Location',
            'Home Town',
            'Role',
            'Skills',
            'Total Experience Years',
            'Total Experience Months',
            'Relevant Experience Years',
            'Relevant Experience Months',
            'Current/ Last Company',
            'Current/ Last CTC Lacs',
            'Current/ Last CTC Thousands',
            'Expected CTC Lacs',
            'Expected CTC Thousands',
            'Notice Period',
            'Comments'
        ];

        $sample = [
            'ABC',
            'John Doe',
            '<EMAIL>',
            '1234567890',
            'Bachelor of Science',
            'New York',
            'New York',
            'Software Engineer',
            'PHP, Laravel, MySQL',
            '5',
            '6',
            '3',
            '9',
            'ABC Company',
            '60',
            '50',
            '70',
            '25',
            '30',
            'Great candidate!'
        ];

        $path = storage_path('app/public/resume_template.csv');
        $file = fopen($path, 'w');

        fputcsv($file, $headers);
        fputcsv($file, $sample);

        fclose($file);

        return response()->download($path, 'resume_template.csv');
    }

    public function unlockResume($resumeId)
    {
        $user = Auth::user();
        $resume = Resume::findOrFail($resumeId);

        // Calculate new points after deduction (deduction is already included in points)
        $newPoints = $user->points - $user->view_points_deduction;


        // Check if user would exceed their total points
        if ($newPoints < 0) {
            $this->dispatch(
                'notify',
                message: 'You do not have enough credits to unlock this resume.',
                type: 'error'
            );
            return;
        }

        // Update user points
        $user->points = $newPoints;
        $user->save();

        $this->dispatch('points-updated', points: $user->points);


        // Mark resume as unlocked for this user
        ResumeUnlock::updateOrCreate([
            'resume_id' => $resumeId,
            'user_id' => $user->id,
        ], [
            'viewed' => true,
        ]);

        $this->dispatch(
            'notify',
            message: 'Resume unlocked successfully!',
            type: 'success'
        );
    }

    public function downloadResume($resumeId)
    {
        $user = Auth::user();
        $resume = Resume::findOrFail($resumeId);

        if (!$resume->resume_file) {
            $this->dispatch(
                'notify',
                message: 'Resume file not found.',
                type: 'error'
            );
            return;
        }

        // Calculate new points after deduction (deduction is already included in points)
        $newPoints = $user->points - $user->download_points_deduction;


        // Check if user would exceed their total points
        if ($newPoints < 0) {
            $this->dispatch(
                'notify',
                message: 'You do not have enough credits to download this resume.',
                type: 'error'
            );
            return;
        }

        // Update user points
        $user->points = $newPoints;
        $user->save();

        $this->dispatch('points-updated', points: $user->points);

        // Mark resume as downloaded for this user
        ResumeUnlock::updateOrCreate([
            'user_id' => $user->id,
            'resume_id' => $resumeId,
        ], [
            'downloaded' => true,
        ]);

        $this->dispatch(
            'notify',
            message: 'Resume downloaded successfully!',
            type: 'success'
        );
    }

    public function processDownloadResume($resumeId)
    {
        $resume = Resume::findOrFail($resumeId);

        // Sanitize the filename by removing slashes and using basename
        $safeFilename = basename(str_replace(['/', '\\'], '_', $resume->resume_file_name));

        if (auth()->user()->role == 'admin') {
            $this->dispatch(
                'notify',
                message: 'Resume downloaded successfully!',
                type: 'success'
            );
            return Storage::disk('s3')->download($resume->resume_file, $safeFilename);
        } else {
            $isDownloaded = ResumeUnlock::where('user_id', auth()->id())
                ->where('resume_id', $resumeId)
                ->where('downloaded', true)
                ->exists();
            if (!$isDownloaded) {
                $this->downloadResume($resumeId);
                return Storage::disk('s3')->download($resume->resume_file, $safeFilename);
            } else {
                $this->dispatch(
                    'notify',
                    message: 'Resume downloaded successfully!',
                    type: 'success'
                );
                return Storage::disk('s3')->download($resume->resume_file, $safeFilename);
            }
        }
    }

    public function confirmDeleteSelectedResumes()
    {
        if (empty($this->selectedResumes)) {
            return;
        }

        // show modal
        Flux::modal('confirm-delete-selected-resumes')->show();
    }

    public function deleteSelectedResumes()
    {
        if (empty($this->selectedResumes)) {
            return;
        }

        // Use soft delete for selected resumes
        Resume::whereIn('id', $this->selectedResumes)->delete();
        $this->selectedResumes = [];
        $this->dispatch(
            'notify',
            message: 'Resumes deleted successfully!',
            type: 'success'
        );
        Flux::modal('confirm-delete-selected-resumes')->close();
    }

    /**
     * Permanently delete selected resumes and their files
     * This should only be used by admins when needed
     */
    public function forceDeleteSelectedResumes()
    {
        if (empty($this->selectedResumes)) {
            return;
        }

        $resumes = Resume::withTrashed()->whereIn('id', $this->selectedResumes)->get();

        foreach ($resumes as $resume) {
            if ($resume->resume_file) {
                Storage::disk('public')->delete($resume->resume_file);
            }
            $resume->forceDelete();
        }

        $this->selectedResumes = [];
        $this->dispatch(
            'notify',
            message: 'Resumes permanently deleted!',
            type: 'success'
        );
        Flux::modal('confirm-delete-selected-resumes')->close();
    }

    public function downloadResumes()
    {

        // Determine if we're exporting selected resumes or all resumes
        $query = Resume::query();

        $user = Auth::user();

        if ($this->showCareerFluteResumes && $user->executive_role == 'Partner') {
            $query->where(function ($q) use ($user) {
                $q->where('partner_id', '!=', $user->id)
                    ->orWhereNull('partner_id');
            });
        } elseif (!$this->showCareerFluteResumes && $user->executive_role == 'Partner') {
            $query->where('partner_id', $user->id);
        }

        if (!empty($this->selectedResumes)) {
            // Export only selected resumes
            $query->whereIn('id', $this->selectedResumes);
        } else {
            // Export all resumes based on current filters
            $this->applyFiltersToQuery($query);
        }

        // Get resumes with optimized query (select only needed fields for export)
        // Exclude resume_content to improve performance as it's not needed for CSV export
        $resumes = $query->select([
            'id',
            'partner_name',
            'candidate_name',
            'country_code',
            'phone_number',
            'email',
            'skills',
            'total_experience_years',
            'total_experience_months',
            'relevant_experience_years',
            'relevant_experience_months',
            'current_company',
            'designation',
            'current_location',
            'home_town',
            'qualification',
            'preferred_location',
            'current_salary_lacs',
            'current_salary_thousands',
            'expected_salary_lacs',
            'expected_salary_thousands',
            'notice_period',
            'comments'
        ])->get();

        if ($resumes->isEmpty()) {
            $this->dispatch(
                'notify',
                message: 'No resumes found to export.',
                type: 'error'
            );
            return;
        }

        $totalRows = $resumes->count();


        // Create CSV file
        $filename = 'resumes_export_' . now()->format('Y-m-d_His') . '.csv';
        $path = storage_path('app/public/' . $filename);

        $file = fopen($path, 'w');

        // Add headers
        $headers = [
            'Partner Name',
            'Candidate Name',
            'Candidate Email ID',
            'Candidate Phone Number',
            'Education',
            'Current Location',
            'Home Town',
            'Role',
            'Skills',
            'Total Experience Years',
            'Total Experience Months',
            'Relevant Experience Years',
            'Relevant Experience Months',
            'Current/ Last Company',
            'Current/ Last CTC Lacs',
            'Current/ Last CTC Thousands',
            'Expected CTC Lacs',
            'Expected CTC Thousands',
            'Notice Period',
            'Comments'
        ];

        fputcsv($file, $headers);

        // Add data rows in chunks to optimize memory usage
        foreach ($resumes as $resume) {
            $row = [
                $resume->partner_name,
                $resume->candidate_name,
                $resume->email,
                $resume->phone_number,
                $resume->qualification,
                $resume->current_location,
                $resume->home_town,
                $resume->designation,
                is_array($resume->skills) ? implode(', ', $resume->skills) : $resume->skills,
                $resume->total_experience_years,
                $resume->total_experience_months,
                $resume->relevant_experience_years,
                $resume->relevant_experience_months,
                $resume->current_company,
                $resume->current_salary_lacs,
                $resume->current_salary_thousands,
                $resume->expected_salary_lacs,
                $resume->expected_salary_thousands,
                $resume->notice_period,
                $resume->comments
            ];

            fputcsv($file, $row);
        }

        fclose($file);


        // Notify user of successful export
        $this->dispatch(
            'notify',
            message: "Successfully exported {$totalRows} " . ($totalRows === 1 ? 'resume' : 'resumes') . ".",
            type: 'success'
        );

        // Return download response
        return response()->download($path, $filename, [
            'Content-Type' => 'text/csv',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Download all resume files as a ZIP archive
     * If resumes are selected, only download those, otherwise download all based on current filters
     */
    public function downloadResumeFiles()
    {
        // Check if ZipArchive is available
        if (!class_exists('ZipArchive')) {
            $this->dispatch(
                'notify',
                message: 'ZIP functionality is not available on the server.',
                type: 'error'
            );
            return;
        }

        // Determine if we're downloading selected resumes or all resumes
        $query = Resume::query();

        if (!empty($this->selectedResumes)) {
            // Download only selected resumes
            $query->whereIn('id', $this->selectedResumes);
        } else {
            // Download all resumes based on current filters
            $this->applyFiltersToQuery($query);
        }

        // Get resumes with resume_file field (select only needed fields for file download)
        $resumes = $query->whereNotNull('resume_file')
            ->select(['id', 'candidate_name', 'resume_file', 'resume_file_name'])
            ->get();

        if ($resumes->isEmpty()) {
            $this->dispatch(
                'notify',
                message: 'No resume files found to download.',
                type: 'error'
            );
            return;
        }

        // Create a temporary directory to store files
        $tempDir = storage_path('app/temp');
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        // Create ZIP file
        $zipFilename = 'resume_files_' . now()->format('Y-m-d_His') . '.zip';
        $zipPath = storage_path('app/public/' . $zipFilename);

        $zip = new ZipArchive();
        if ($zip->open($zipPath, ZipArchive::CREATE) !== true) {
            $this->dispatch(
                'notify',
                message: 'Could not create ZIP file.',
                type: 'error'
            );
            return;
        }

        $addedFiles = 0;
        $failedFiles = 0;

        // Add each resume file to the ZIP
        foreach ($resumes as $resume) {
            try {
                // Skip if no file
                if (!$resume->resume_file) {
                    continue;
                }

                // Create a safe filename for the ZIP
                $originalName = $resume->resume_file_name ?? basename($resume->resume_file);

                // Sanitize the filename by removing slashes and using basename
                $safeFilename = preg_replace('/[^a-zA-Z0-9_.-]/', '_', $resume->candidate_name) . '_' . basename($originalName);

                // Get file content from S3
                $fileContent = Storage::disk('s3')->get($resume->resume_file);

                if ($fileContent) {
                    // Add file to ZIP with sanitized filename (no directory structure)
                    $zip->addFromString($safeFilename, $fileContent);
                    $addedFiles++;
                } else {
                    $failedFiles++;
                }
            } catch (\Exception $e) {
                $failedFiles++;
                // Log the error for debugging
                \Illuminate\Support\Facades\Log::error('ZIP error: ' . $e->getMessage());
                // Continue with other files even if one fails
                continue;
            }
        }

        $zip->close();

        if ($addedFiles === 0) {
            // No files were added to the ZIP
            if (file_exists($zipPath)) {
                unlink($zipPath);
            }

            $this->dispatch(
                'notify',
                message: 'Could not add any files to the ZIP archive.',
                type: 'error'
            );
            return;
        }

        // Notify user of successful download
        $message = "Successfully added {$addedFiles} " . ($addedFiles === 1 ? 'file' : 'files') . " to ZIP archive.";
        if ($failedFiles > 0) {
            $message .= " {$failedFiles} " . ($failedFiles === 1 ? 'file' : 'files') . " could not be added.";
        }

        $this->dispatch(
            'notify',
            message: $message,
            type: 'success'
        );

        // Return download response
        return response()->download($zipPath, $zipFilename, [
            'Content-Type' => 'application/zip',
        ])->deleteFileAfterSend(true);
    }

    /**
     * Highlight search terms in text
     *
     * @param string|array $text The text to search in
     * @return string Text with highlighted search terms
     */
    public function highlightSearchTerm($text)
    {
        if (empty($this->search) || empty($text)) {
            return $text;
        }

        // Handle array input (like skills)
        if (is_array($text)) {
            return $text; // Return as is, we handle arrays separately
        }

        // Split search terms by comma and trim whitespace
        $searchTerms = array_map('trim', explode(',', $this->search));

        // Process each search term - highlight all terms found
        foreach ($searchTerms as $term) {
            if (empty($term)) continue;

            $quotedTerm = preg_quote($term, '/');
            $text = preg_replace('/(' . $quotedTerm . ')/i', '<span class="bg-yellow-200 dark:bg-yellow-700">$1</span>', $text);
        }

        return $text;
    }

    /**
     * Highlight search terms in an array of skills
     *
     * @param array $skills The skills array to highlight search terms in
     * @return array Array of skills with highlighted search terms
     */
    public function highlightSkills($skills)
    {
        if (empty($this->search) || empty($skills) || !is_array($skills)) {
            return $skills;
        }

        // Split search terms by comma and trim whitespace
        $searchTerms = array_map('trim', explode(',', $this->search));
        $highlightedSkills = $skills;

        foreach ($skills as $index => $skill) {
            $highlightedSkill = $skill;

            // Highlight all search terms found in this skill
            foreach ($searchTerms as $term) {
                if (empty($term)) continue;

                $quotedTerm = preg_quote($term, '/');
                $highlightedSkill = preg_replace('/(' . $quotedTerm . ')/i', '<span class="bg-yellow-200 dark:bg-yellow-700">$1</span>', $highlightedSkill);
            }

            $highlightedSkills[$index] = $highlightedSkill;
        }

        return $highlightedSkills;
    }

    protected $listeners = [
        'city-selected' => 'handleCitySelected',
        'job-description-selected' => 'handleJobDescriptionSelected',
        'job-description-cleared' => 'handleJobDescriptionCleared'
    ];

    public function handleCitySelected($data)
    {
        if ($data['field'] == 'location') {
            $this->location = $data['city'];
        }
    }

    public function updatedShowCareerFluteResumes()
    {
        if ($this->showCareerFluteResumes) {
            $this->resumeType = 'all';
        }
    }

    public function render()
    {
        $user = Auth::user();

        if (($this->search || $this->minExperienceYears || $this->minExperienceMonths ||
                $this->maxExperienceYears || $this->maxExperienceMonths ||
                $this->minSalaryLacs || $this->minSalaryThousands ||
                $this->maxSalaryLacs || $this->maxSalaryThousands ||
                $this->location || $this->noticePeriod || $this->startDate || $this->endDate) ||
            ($user->role == 'admin') || !$this->showCareerFluteResumes
        ) {

            $query = Resume::query();

            $authUser = Auth::user();


            if ($this->showCareerFluteResumes && $authUser->executive_role == 'Partner') {
                $query->where(function ($q) use ($authUser) {
                    $q->where('partner_id', '!=', $authUser->id)
                        ->orWhereNull('partner_id');
                });
            } elseif (!$this->showCareerFluteResumes && $authUser->executive_role == 'Partner') {
                $query->where('partner_id', $authUser->id);
            }


            // Optimize query by selecting only necessary fields for display
            // Exclude resume_content unless needed for search to improve performance
            $selectFields = [
                'id',
                'partner_id',
                'candidate_name',
                'country_code',
                'phone_number',
                'email',
                'skills',
                'total_experience_years',
                'total_experience_months',
                'relevant_experience_years',
                'relevant_experience_months',
                'current_company',
                'designation',
                'current_location',
                'home_town',
                'qualification',
                'preferred_location',
                'current_salary_lacs',
                'current_salary_thousands',
                'expected_salary_lacs',
                'expected_salary_thousands',
                'notice_period',
                'partner_name',
                'comments',
                'is_archived',
                'submitted_from',
                'created_at',
                'updated_at',
                'deleted_at',
                'resume_file'
            ];

            // Only include resume_content if we have a search term (needed for content search)
            if ($this->search) {
                $selectFields[] = 'resume_content';
            }

            $query->select($selectFields);

            // Apply all filters using the reusable function
            $this->applyFiltersToQuery($query);

            if ($this->search) {
                $this->dispatch('updatedSearch', search: $this->search);
            }

            $resumes = $query->latest('created_at')->paginate(100);

            // Load unlocked status for resumes if user is executive
            if ($authUser->role === 'executive') {
                // Only get unlocks that are less than 30 days old
                $unlockedResumes = $authUser->unlockedResumes()
                    ->where('updated_at', '>=', now()->subDays(30))
                    ->get();

                // Logic to check viewed and downloaded status
                $resumes->each(function ($resume) use ($unlockedResumes) {
                    $unlockedResume = $unlockedResumes->where('resume_id', $resume->id)->first();
                    $resume->viewed = $unlockedResume ? $unlockedResume->viewed : false;
                    $resume->downloaded = $unlockedResume ? $unlockedResume->downloaded : false;
                });
            }
        } else {
            $resumes = collect();

            $resumes = new LengthAwarePaginator($resumes->forPage(1, 100), $resumes->count(), 100);
        }

        $role = $user->role;
        $partner_id = $user->id;
        $executive_role = $user->executive_role;
        $viewCoins = $user->view_points_deduction;
        $downloadCoins = $user->download_points_deduction;
        $export_points_deduction = $user->export_points_deduction ?? 0;



        return view('livewire.search-candidates', [
            'resumes' => $resumes,
            'role' => $role,
            'partner_id' => $partner_id,
            'executive_role' => $executive_role,
            'viewCoins' => $viewCoins,
            'downloadCoins' => $downloadCoins,
            'export_points_deduction' => $export_points_deduction,
        ]);
    }
}
