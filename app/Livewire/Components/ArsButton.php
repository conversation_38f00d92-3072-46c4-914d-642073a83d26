<?php

namespace App\Livewire\Components;

use App\Models\ArsRanking;
use App\Models\JobDescription;
use App\Models\Resume;
use App\Services\ArsService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ArsButton extends Component
{
    public $resumeId;
    public $jobDescriptionId;
    public $showRankingModal = false;
    public $rankingResult = null;
    public $loadingRanking = false;
    public $existingRanking = null;
    public $showFeedbackTooltip = false;

    public function mount($resumeId, $jobDescriptionId)
    {
        $this->resumeId = $resumeId;
        $this->jobDescriptionId = $jobDescriptionId;
        $this->loadExistingRanking();

    }

    protected function loadExistingRanking()
    {
        // Delete ARS rankings older than one month
        ArsRanking::where('created_at', '<', now()->subMonth())->delete();

        // Check if there's an existing ranking for this resume and job description
        $this->existingRanking = ArsRanking::where('resume_id', $this->resumeId)
            ->where('job_description_id', $this->jobDescriptionId)
            ->where('user_id', Auth::id())
            ->first();
    }

    public function toggleFeedbackTooltip()
    {
        $this->showFeedbackTooltip = !$this->showFeedbackTooltip;
    }

    public function rankResume()
    {
        $this->loadingRanking = true;
        $this->showRankingModal = true;

        try {
            $resume = Resume::findOrFail($this->resumeId);
            $jobDescription = JobDescription::findOrFail($this->jobDescriptionId);
            $user = Auth::user();

            $arsService = new ArsService();
            $result = $arsService->rankResume($resume, $jobDescription, $user);

            if ($result['success']) {
                $this->rankingResult = $result['ranking'];
                $this->existingRanking = $result['ranking'];
                $this->dispatch('points-updated', points: $user->points);
            } else {
                $this->dispatch(
                    'notify',
                    message: $result['message'],
                    type: 'error'
                );
                $this->showRankingModal = false;
            }
        } catch (\Exception $e) {
            $this->dispatch(
                'notify',
                message: 'Failed to rank resume: ' . $e->getMessage(),
                type: 'error'
            );
            $this->showRankingModal = false;
        }

        $this->loadingRanking = false;
    }

    public function closeRankingModal()
    {
        $this->showRankingModal = false;
        $this->rankingResult = null;
    }

    // Note: This method is no longer used as we're handling the copy directly in the blade file
    // We're keeping it for backward compatibility
    public function copyFeedback(string $source = '')
    {
        // This method is now a no-op as we're handling the copy directly in the blade file
        // $source parameter is intentionally unused
        return '';
    }

    public function render()
    {
        return view('livewire.components.ars-button');
    }
}
