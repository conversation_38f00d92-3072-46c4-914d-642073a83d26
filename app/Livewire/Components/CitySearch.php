<?php

namespace App\Livewire\Components;

use App\Models\City;
use Livewire\Component;

class CitySearch extends Component
{
    public $query = '';
    public $cities = [];
    public $highlightIndex = 0;
    public $showDropdown = false;
    public $selectedCity = '';
    public $field = '';

    protected $listeners = ['clear' => 'clear', 'update-selected-location' => 'handleUpdateSelectedLocation'];

    public function mount($selectedCity = '', $field = '')
    {
        $this->selectedCity = $selectedCity;
        $this->query = $selectedCity;
        $this->field = $field;
    }

    public function updatedQuery()
    {
        $this->resetValidation();

        if (strlen($this->query) < 2) {
            $this->cities = [];
            $this->showDropdown = false;
            return;
        }

        $this->cities = City::where('name', 'like', '%' . $this->query . '%')
        ->orderByRaw("CASE WHEN name = ? THEN 0 WHEN name LIKE ? THEN 1 ELSE 2 END",
            [$this->query, $this->query . '%'])
        ->limit(10)
        ->get();

        $this->highlightIndex = 0;
        $this->showDropdown = true;
    }

    public function selectCity($city)
    {
        $this->query = $city;
        $this->selectedCity = $city;
        $this->showDropdown = false;

        // Emit the selected city to the parent component
        $this->dispatch('city-selected', [
            'city' => $city,
            'field' => $this->field
        ]);
    }

    public function incrementHighlight()
    {
        if ($this->highlightIndex === count($this->cities) - 1) {
            $this->highlightIndex = 0;
            return;
        }
        $this->highlightIndex++;
    }

    public function decrementHighlight()
    {
        if ($this->highlightIndex === 0) {
            $this->highlightIndex = count($this->cities) - 1;
            return;
        }
        $this->highlightIndex--;
    }

    public function selectHighlighted()
    {
        if (count($this->cities) > 0) {
            $this->selectCity($this->cities[$this->highlightIndex]->name);
        } else if (strlen($this->query) >= 2) {
            // If no cities in dropdown but user has typed something, use that value
            $this->selectCity($this->query);
        }
    }

    public function handleBlur()
    {
        // When input loses focus, if there's text, use it as the selected city
        if (strlen($this->query) >= 2) {
            $this->selectCity($this->query);
        }
        $this->showDropdown = false;
    }

    public function clear()
    {
        $this->query = '';
        $this->selectedCity = '';
        $this->cities = [];
        $this->showDropdown = false;
    }

    public function handleUpdateSelectedLocation($current_location = '', $home_town = '')
    {
        if ($this->field === 'current_location') {
            $this->query = $current_location;
            $this->selectedCity = $current_location;
        } elseif ($this->field === 'home_town') {
            $this->query = $home_town;
            $this->selectedCity = $home_town;
        }
    }

    public function render()
    {
        return view('livewire.components.city-search');
    }
}
