<?php

namespace App\Livewire\Components;

use App\Models\JobDescription;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ArsFilterButton extends Component
{
    public $showSelectModal = false;
    public $search = '';
    public $selectedJobDescriptionId = null;
    public $selectedJobDescription = null;

    public function mount()
    {
        // Check if there's a selected job description in the session
        if (session()->has('selected_job_description_id')) {
            $this->selectedJobDescriptionId = session('selected_job_description_id');
            $this->loadSelectedJobDescription();
        }
    }

    public function openSelectModal()
    {
        $this->reset(['search']);
        $this->showSelectModal = true;
    }

    public function closeSelectModal()
    {
        $this->showSelectModal = false;
    }

    public function selectJobDescription($id)
    {
        $this->selectedJobDescriptionId = $id;
        $this->loadSelectedJobDescription();

        // Store the selected job description ID in the session
        session(['selected_job_description_id' => $id]);

        // Dispatch an event to notify the parent component
        $this->dispatch('job-description-selected', jobDescriptionId: $id);

        $this->closeSelectModal();
    }

    public function clearJobDescription()
    {
        $this->selectedJobDescriptionId = null;
        $this->selectedJobDescription = null;

        // Remove the selected job description ID from the session
        session()->forget('selected_job_description_id');

        // Dispatch an event to notify the parent component
        $this->dispatch('job-description-cleared');
    }

    protected function loadSelectedJobDescription()
    {
        if ($this->selectedJobDescriptionId) {
            $this->selectedJobDescription = JobDescription::find($this->selectedJobDescriptionId);
        }
    }

    public function render()
    {
        $jobDescriptions = [];

        if ($this->showSelectModal) {
            $query = JobDescription::where('user_id', Auth::id());

            if ($this->search) {
                $query->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%');
            }

            $jobDescriptions = $query->orderBy('created_at', 'desc')->get();
        }

        return view('livewire.components.ars-filter-button', [
            'jobDescriptions' => $jobDescriptions
        ]);
    }
}
