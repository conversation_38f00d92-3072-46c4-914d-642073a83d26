<?php

namespace App\Livewire\Components;

use App\Models\User;
use Livewire\Component;

class PartnerSearch extends Component
{
    public $query = '';
    public $partners = [];
    public $highlightIndex = 0;
    public $showDropdown = false;
    public $selectedPartner = '';
    public $selectedPartnerId = null;
    public $selectedPartnerEmail = '';
    public $selectedPartnerPhone = '';
    public $field = '';

    protected $listeners = ['clear' => 'clear'];

    public function mount($selectedPartner = '', $field = '')
    {
        $this->selectedPartner = $selectedPartner;
        $this->query = $selectedPartner;
        $this->field = $field;
    }

    public function updatedQuery()
    {
        $this->resetValidation();

        if (strlen($this->query) < 2) {
            $this->partners = [];
            $this->showDropdown = false;
            return;
        }

        $this->partners = User::where('name', 'like', '%' . $this->query . '%')
            ->orderByRaw("CASE WHEN name = ? THEN 0 WHEN name LIKE ? THEN 1 ELSE 2 END",
                [$this->query, $this->query . '%'])
            ->where('role', 'executive')
            ->where('executive_role', 'Partner')
            ->limit(10)
            ->get(['id', 'name', 'email', 'phone_number']);

        $this->highlightIndex = 0;
        $this->showDropdown = true;
    }

    public function selectPartner($partnerId, $partnerName, $partnerEmail, $partnerPhone)
    {
        $this->query = $partnerName;
        $this->selectedPartner = $partnerName;
        $this->selectedPartnerId = $partnerId;
        $this->selectedPartnerEmail = $partnerEmail;
        $this->selectedPartnerPhone = $partnerPhone;
        $this->showDropdown = false;

        // Emit the selected partner to the parent component
        $this->dispatch('partner-selected', [
            'partner_id' => $partnerId,
            'partner_name' => $partnerName,
            'partner_email' => $partnerEmail,
            'partner_phone' => $partnerPhone,
            'field' => $this->field
        ]);
    }

    public function incrementHighlight()
    {
        if ($this->highlightIndex === count($this->partners) - 1) {
            $this->highlightIndex = 0;
            return;
        }
        $this->highlightIndex++;
    }

    public function decrementHighlight()
    {
        if ($this->highlightIndex === 0) {
            $this->highlightIndex = count($this->partners) - 1;
            return;
        }
        $this->highlightIndex--;
    }

    public function selectHighlighted()
    {
        if (count($this->partners) > 0) {
            $partner = $this->partners[$this->highlightIndex];
            $this->selectPartner($partner->id, $partner->name, $partner->email, $partner->phone_number);
        } else if (strlen($this->query) >= 2) {
            // If no partners in dropdown but user has typed something, use that value as custom partner
            $this->selectPartner(null, $this->query, '', '');
        }
    }

    public function handleBlur()
    {
        // When input loses focus, if there's text, use it as the selected partner
        if (strlen($this->query) >= 2) {
            $this->selectPartner(null, $this->query, '', '');
        }
        $this->showDropdown = false;
    }

    public function clear()
    {
        $this->query = '';
        $this->selectedPartner = '';
        $this->selectedPartnerId = null;
        $this->selectedPartnerEmail = '';
        $this->selectedPartnerPhone = '';
        $this->partners = [];
        $this->showDropdown = false;
    }

    public function render()
    {
        return view('livewire.components.partner-search');
    }
}
