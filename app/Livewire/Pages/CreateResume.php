<?php

namespace App\Livewire\Pages;

use App\Models\Resume;
use App\Models\User;
use Livewire\Attributes\Rule;
use Livewire\Component;
use Livewire\WithFileUploads;
use App\Http\Controllers\Api\ResumeParserController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;
use PhpOffice\PhpWord\IOFactory;
use Smalot\PdfParser\Parser;
use Illuminate\Support\Str;

class CreateResume extends Component
{
    use WithFileUploads;

    public $resumeId = null;

    #[Rule('nullable|file|mimes:pdf,doc,docx')]
    public $resume_file;

    public $resume_file_url;

    #[Rule('nullable|string|max:255')]
    public $candidate_name = null;

    #[Rule('nullable|string|max:40')]
    public $phone_number = null;

    #[Rule('required|email|max:255')]
    public $email = null;

    #[Rule('nullable|array')]
    public $skills = [];

    #[Rule('nullable')]
    public $total_experience_years = 0;

    #[Rule('nullable')]
    public $total_experience_months = 0;

    #[Rule('nullable|string|max:255')]
    public $current_company = null;

    #[Rule('nullable|string|max:255')]
    public $designation = null;

    #[Rule('nullable|string|max:255')]
    public $current_location = null;

    #[Rule('nullable|string|max:255')]
    public $preferred_location = null;

    #[Rule('nullable')]
    public $current_salary_lacs = 0;

    #[Rule('nullable')]
    public $current_salary_thousands = 0;

    #[Rule('nullable')]
    public $expected_salary_lacs = 0;

    #[Rule('nullable')]
    public $expected_salary_thousands = 0;

    #[Rule('nullable|string|max:255')]
    public $country_code = null;

    #[Rule('nullable')]
    public $notice_period = 0;

    #[Rule('nullable|string|max:255')]
    public $qualification = null;

    #[Rule('nullable')]
    public $relevant_experience_years = 0;

    #[Rule('nullable')]
    public $relevant_experience_months = 0;

    #[Rule('nullable|string|max:255')]
    public $home_town = null;

    #[Rule('nullable|string|max:255')]
    public $university = null;

    #[Rule('nullable|string|max:255')]
    public $industry = null;

    #[Rule('nullable|string|max:255')]
    public $partner_name = null;

    #[Rule('nullable|email|max:255')]
    public $partner_email = null;

    #[Rule('nullable|string|max:40')]
    public $partner_phone_number = null;

    public $partner_id = null;

    #[Rule('nullable|max:50000')]
    public $resume_content = null;

    #[Rule('nullable|max:1000')]
    public $comments = null;


    public function mount($id = null)
    {
        if ($id) {
            $this->resumeId = $id;
            $resume = Resume::findOrFail($id);

            $this->candidate_name = $resume->candidate_name;
            $this->phone_number = $resume->phone_number;
            $this->email = $resume->email;
            $this->skills = $resume->skills;
            $this->total_experience_years = $resume->total_experience_years ?? 0;
            $this->total_experience_months = $resume->total_experience_months ?? 0;
            $this->current_company = $resume->current_company;
            $this->current_location = $resume->current_location;
            $this->notice_period = $resume->notice_period ?? 0;
            $this->preferred_location = $resume->preferred_location;
            $this->current_salary_lacs = $resume->current_salary_lacs ?? 0;
            $this->current_salary_thousands = $resume->current_salary_thousands ?? 0;
            $this->expected_salary_lacs = $resume->expected_salary_lacs ?? 0;
            $this->expected_salary_thousands = $resume->expected_salary_thousands ?? 0;
            $this->country_code = $resume->country_code;
            $this->current_company = $resume->current_company;
            $this->designation = $resume->designation;
            $this->qualification = $resume->qualification;
            $this->university = $resume->university;
            $this->industry = $resume->industry;
            $this->resume_content = $resume->resume_content;
            $this->resume_file_url = $resume->resume_file_url;
            $this->home_town = $resume->home_town;
            $this->relevant_experience_years = $resume->relevant_experience_years ?? 0;
            $this->relevant_experience_months = $resume->relevant_experience_months ?? 0;
            $this->partner_name = $resume->partner_name;
            $this->partner_id = $resume->partner_id;

            // If there's a partner_id, load the partner's email and phone
            if ($resume->partner_id) {
                $partner = User::find($resume->partner_id);
                if ($partner) {
                    $this->partner_email = $partner->email;
                    $this->partner_phone_number = $partner->phone_number;
                }
            }

            $this->comments = $resume->comments;
        }
    }

    public function updatedResumeFile()
    {
        if (!$this->resume_file) {
            return;
        }

        try {
            // Generate unique file hash based on content
            $fileContent = file_get_contents($this->resume_file->getRealPath());
            $fileHash = md5($fileContent);

            // Check if we have cached results for this file
            $cacheKey = 'resume_parse_' . $fileHash;
            if (Cache::has($cacheKey)) {
                $parsedData = Cache::get($cacheKey);
                $this->resume_content = $parsedData['resume_content'] ?? '';

                // Assign cached parsed data to properties
                $this->candidate_name = $parsedData['candidate_name'] ?? '';
                $this->phone_number = $parsedData['phone_number'] ?? '';
                $this->email = $parsedData['email'] ?? '';
                $this->skills = is_array($parsedData['skills']) ? $parsedData['skills'] : explode(',', $parsedData['skills'] ?? '');
                $this->total_experience_years = $parsedData['total_experience_years'] ?? 0;
                $this->total_experience_months = $parsedData['total_experience_months'] ?? 0;
                $this->current_company = $parsedData['current_company'] ?? null;
                $this->current_location = $parsedData['current_location'] ?? '';
                $this->notice_period = $parsedData['notice_period'] ?? 0;
                $this->preferred_location = $parsedData['preferred_location'] ?? '';
                $this->current_salary_lacs = $parsedData['current_salary_lacs'] ?? 0;
                $this->current_salary_thousands = $parsedData['current_salary_thousands'] ?? 0;
                $this->expected_salary_lacs = $parsedData['expected_salary_lacs'] ?? 0;
                $this->expected_salary_thousands = $parsedData['expected_salary_thousands'] ?? 0;
                $this->designation = $parsedData['designation'] ?? '';
                $this->qualification = $parsedData['qualification'] ?? '';
                $this->relevant_experience_years = $parsedData['relevant_experience_years'] ?? 0;
                $this->relevant_experience_months = $parsedData['relevant_experience_months'] ?? 0;

                $this->dispatch('update-selected-location', current_location: $this->current_location, home_town: $this->home_town);

                $this->dispatch(
                    'notify',
                    message: 'Resume parsed successfully from cache!',
                    type: 'success'
                );
                return;
            }

            $extension = $this->resume_file->getClientOriginalExtension();


            // Extract text based on file type
            if (strtolower($extension) === 'pdf') {
                $text = $this->extractTextFromPdf($this->resume_file->getRealPath());
            } else {
                try {
                    $phpWord = IOFactory::load($this->resume_file->getRealPath());
                    $text = $this->extractTextFromWord($phpWord);
                } catch (\Exception $e) {
                    Log::error('Error loading Word file: ' . $e->getMessage());

                    // If the error is related to invalid images, try to extract text without images
                    if (strpos($e->getMessage(), 'Invalid image') !== false) {
                        Log::info('Attempting to extract text while skipping corrupted images');
                        try {
                            $phpWord = IOFactory::load($this->resume_file->getRealPath());
                            $text = $this->extractTextFromWordSafely($phpWord);
                        } catch (\Exception $fallbackException) {
                            Log::error('Fallback Word parsing also failed: ' . $fallbackException->getMessage());
                            // Try the original fallback method
                            $tempPath = storage_path('app/temp/' . uniqid() . '.' . $extension);
                            if (!is_dir(storage_path('app/temp'))) {
                                mkdir(storage_path('app/temp'), 0755, true);
                            }

                            file_put_contents($tempPath, file_get_contents($this->resume_file->getRealPath()));
                            $phpWord = IOFactory::load($tempPath);
                            $text = $this->extractTextFromWordSafely($phpWord);
                            // Clean up temp file
                            @unlink($tempPath);
                        }
                    } else {
                        // Try to get the file content directly
                        $tempPath = storage_path('app/temp/' . uniqid() . '.' . $extension);
                        if (!is_dir(storage_path('app/temp'))) {
                            mkdir(storage_path('app/temp'), 0755, true);
                        }

                        file_put_contents($tempPath, file_get_contents($this->resume_file->getRealPath()));
                        $phpWord = IOFactory::load($tempPath);
                        $text = $this->extractTextFromWord($phpWord);
                        // Clean up temp file
                        @unlink($tempPath);
                    }
                }
            }

            if (empty(trim($text))) {
                $this->addError('resume_file', 'Could not extract text from the resume. Please try a different file or fill in the details manually.');
                return;
            }

            // Initialize ResumeParserController
            $parser = new ResumeParserController();

            $parsedData = $parser->parseTextWithChatGPTSystem($text, $this->resume_file);

            $this->resume_content = $text;

            if ($parsedData) {
                // Assign parsed data to properties
                $this->candidate_name = $parsedData['candidate_name'] ?? '';
                $this->phone_number = $parsedData['phone_number'] ?? '';
                $this->email = $parsedData['email'] ?? '';
                $this->skills = is_array($parsedData['skills']) ? $parsedData['skills'] : explode(',', $parsedData['skills'] ?? '');
                $this->total_experience_years = $parsedData['total_experience_years'] ?? 0;
                $this->total_experience_months = $parsedData['total_experience_months'] ?? 0;
                $this->current_company = $parsedData['current_company'] ?? null;
                $this->current_location = $parsedData['current_location'] ?? '';
                $this->notice_period = $parsedData['notice_period'] ?? 0;
                $this->preferred_location = $parsedData['preferred_location'] ?? '';
                $this->current_salary_lacs = $parsedData['current_salary_lacs'] ?? 0;
                $this->current_salary_thousands = $parsedData['current_salary_thousands'] ?? 0;
                $this->expected_salary_lacs = $parsedData['expected_salary_lacs'] ?? 0;
                $this->expected_salary_thousands = $parsedData['expected_salary_thousands'] ?? 0;
                $this->designation = $parsedData['designation'] ?? '';
                $this->qualification = $parsedData['qualification'] ?? '';
                $this->relevant_experience_years = $parsedData['relevant_experience_years'] ?? 0;
                $this->relevant_experience_months = $parsedData['relevant_experience_months'] ?? 0;

                // Cache the parsed data for 3 months (include resume_content for cache)
                $cacheData = $parsedData;
                $cacheData['resume_content'] = $text;
                Cache::put($cacheKey, $cacheData, now()->addMonths(3));

                $this->dispatch(
                    'notify',
                    message: 'Resume parsed successfully!',
                    type: 'success'
                );
            } else {
                Log::warning('Failed to parse resume data');
                $this->addError('resume_file', 'Failed to parse resume. Please fill in the details manually.');
                $this->dispatch(
                    'notify',
                    message: 'Failed to parse resume. Please fill in the details manually.',
                    type: 'warning'
                );
            }
        } catch (\Exception $e) {
            $this->addError('resume_file', 'Error processing resume. Please try again or fill in the details manually.');
            $this->dispatch(
                'notify',
                message: 'Error processing resume: ' . $e->getMessage(),
                type: 'error'
            );
            Log::error('Resume parsing error: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
        }
    }

    public function submit()
    {
        // Validate the form data
        $this->validate();
        $this->validate([
            'email' => 'required|email|max:255|unique:resumes,email,' . $this->resumeId,
            'phone_number' => 'nullable|string|max:40|unique:resumes,phone_number,' . $this->resumeId,
        ], [
            'email.unique' => 'Email already exists',
            'phone_number.unique' => 'Phone number already exists',
        ]);

        $data = [
            'candidate_name' => $this->candidate_name,
            'phone_number' => $this->phone_number,
            'email' => $this->email,
            'skills' => $this->skills,
            'total_experience_years' => $this->total_experience_years,
            'total_experience_months' => $this->total_experience_months,
            'current_company' => $this->current_company,
            'current_location' => $this->current_location,
            'notice_period' => $this->notice_period ? $this->notice_period : 0,
            'preferred_location' => $this->preferred_location,
            'current_salary_lacs' => $this->current_salary_lacs,
            'current_salary_thousands' => $this->current_salary_thousands,
            'expected_salary_lacs' => $this->expected_salary_lacs,
            'expected_salary_thousands' => $this->expected_salary_thousands,
            'current_company' => $this->current_company,
            'designation' => $this->designation,
            'qualification' => $this->qualification,
            'resume_content' => $this->resume_content,
            'home_town' => $this->home_town,
            'relevant_experience_years' => $this->relevant_experience_years,
            'relevant_experience_months' => $this->relevant_experience_months,
            'partner_name' => $this->partner_name,
            'comments' => $this->comments,
            'is_archived' => false,
        ];

        if ($this->resume_file) {
            $fileName = $this->resume_file->store('resumes', 's3', 'public');
            $data['resume_file'] = $fileName;
            $formattedfileName = $this->candidate_name . '_' . $this->designation . '.' .
                $this->resume_file->getClientOriginalExtension();
            $data['resume_file_name'] = $formattedfileName;
        }

        // Handle partner creation or selection

        // If partner_id is set, use it
        if ($this->partner_id) {
            $data['partner_id'] = $this->partner_id;
        }
        // If partner_email is provided but no partner_id, check if partner exists or create new one
        elseif ($this->partner_email) {
            $existingPartner = User::where('email', $this->partner_email)->first();

            if ($existingPartner) {
                $data['partner_id'] = $existingPartner->id;
            } else {
                $this->validate([
                    'partner_name' => 'required|string|max:255',
                    'partner_email' => 'required|email|max:255|unique:users,email',
                    'partner_phone_number' => 'nullable|string|max:40',
                ]);
                // Create new partner
                $partner = User::create([
                    'name' => $this->partner_name,
                    'email' => $this->partner_email,
                    'phone_number' => $this->partner_phone_number,
                    'role' => 'executive',
                    'executive_role' => 'Partner',
                    'password' => Hash::make(Str::random(8)),
                ]);
                $data['partner_id'] = $partner->id;
            }
        }
        // If current user is a partner, use their ID
        elseif (Auth::check() && Auth::user()->executive_role === 'Partner') {
            $data['partner_id'] = Auth::user()->id;
        }

        if ($this->resumeId) {
            Resume::where('id', $this->resumeId)->update($data);
            $message = 'Resume updated successfully!';
        } else {
            Resume::create($data);
            $message = 'Resume created successfully!';
        }


        session()->flash('message', $message);
        $this->redirect(route('resumes'), navigate: true);
    }

    public function updatedEmail()
    {
        $this->validate([
            'email' => 'required|email|max:255|unique:resumes,email,' . $this->resumeId,
        ], [
            'email.unique' => 'Email already exists',
        ]);
    }

    public function updatedPhoneNumber()
    {
        $this->validate([
            'phone_number' => 'nullable|string|max:40|unique:resumes,phone_number,' . $this->resumeId,
        ], [
            'phone_number.unique' => 'Phone number already exists',
        ]);
    }

    protected $listeners = [
        'city-selected' => 'handleCitySelected',
        'partner-selected' => 'handlePartnerSelected'
    ];

    public function handleCitySelected($data)
    {
        if ($data['field'] === 'current_location') {
            $this->current_location = $data['city'];
        } elseif ($data['field'] === 'home_town') {
            $this->home_town = $data['city'];
        }
    }

    public function handlePartnerSelected($data)
    {
        $this->partner_id = $data['partner_id'];
        $this->partner_name = $data['partner_name'];
        $this->partner_email = $data['partner_email'];
        $this->partner_phone_number = $data['partner_phone'];
    }

    public function render()
    {
        return view('livewire.pages.create-resume');
    }

    /**
     * Extract text from a PHPWord document
     *
     * @param \PhpOffice\PhpWord\PhpWord $phpWord
     * @return string
     */
    private function extractTextFromWord($phpWord)
    {
        $text = '';

        // Extract text from each section
        foreach ($phpWord->getSections() as $section) {
            // Extract headers with error handling
            try {
                foreach ($section->getHeaders() as $header) {
                    foreach ($header->getElements() as $element) {
                        $text .= $this->extractElementText($element) . ' ';
                    }
                    $text .= "\n";
                }
            } catch (\Exception $e) {
                Log::warning('Error processing headers: ' . $e->getMessage());
            }

            // Extract section content with error handling
            try {
                foreach ($section->getElements() as $element) {
                    $text .= $this->extractElementText($element) . ' ';
                }
            } catch (\Exception $e) {
                Log::warning('Error processing section elements: ' . $e->getMessage());
            }

            // Extract footers with error handling
            try {
                foreach ($section->getFooters() as $footer) {
                    foreach ($footer->getElements() as $element) {
                        $text .= $this->extractElementText($element) . ' ';
                    }
                    $text .= "\n";
                }
            } catch (\Exception $e) {
                Log::warning('Error processing footers: ' . $e->getMessage());
            }

            $text .= "\n"; // Add line break after each section
        }

        // Check if text was successfully extracted
        if (empty(trim($text))) {
            Log::warning('Empty text extracted from DOCX: ' . $this->resume_file?->getClientOriginalName());
        }

        return trim($text);
    }

    /**
     * Extract text from a PDF file
     *
     * @param string $filePath
     * @return string
     */
    private function extractTextFromPdf($filePath)
    {
        try {
            $parser = new Parser();

            // First try direct file path approach
            if (file_exists($filePath)) {
                $pdf = $parser->parseFile($filePath);
            } else {
                // If file doesn't exist at the path, try to get content from the UploadedFile object
                try {
                    // Get the file content directly from the UploadedFile object
                    $content = file_get_contents($this->resume_file->getRealPath());
                    if (!$content) {
                        throw new \Exception("Could not read file content from uploaded file");
                    }
                    $pdf = $parser->parseContent($content);
                } catch (\Exception $e) {
                    // If that fails, try to get it from Livewire's temporary storage
                    $path = $this->resume_file->getFilename();
                    $storage = Storage::disk('local');
                    $fullPath = 'livewire-tmp/' . $path;

                    if ($storage->exists($fullPath)) {
                        $content = $storage->get($fullPath);
                        $pdf = $parser->parseContent($content);
                    } else {
                        throw new \Exception("File not found at path: {$filePath} or in livewire-tmp storage");
                    }
                }
            }

            $text = $pdf->getText();

            // Check if text was successfully extracted
            if (empty(trim($text))) {
                Log::warning('Empty text extracted from PDF: ' . $this->resume_file?->getClientOriginalName());
            }

            return $text;
        } catch (\Exception $e) {
            Log::error('PDF parsing error: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Extract text from a PHPWord element
     *
     * @param mixed $element
     * @return string
     */
    private function extractElementText($element)
    {
        $text = '';

        try {
            if ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                foreach ($element->getElements() as $textElement) {
                    try {
                        if ($textElement instanceof \PhpOffice\PhpWord\Element\Text) {
                            $text .= $textElement->getText() . ' ';
                        } elseif ($textElement instanceof \PhpOffice\PhpWord\Element\PreserveText) {
                            $text .= $textElement->getText() . ' ';
                        } elseif ($textElement instanceof \PhpOffice\PhpWord\Element\Link) {
                            $text .= $textElement->getText() . ' ';
                        } elseif ($textElement instanceof \PhpOffice\PhpWord\Element\Image) {
                            // Skip images gracefully - they don't contain extractable text
                            Log::info('Skipping image element during text extraction');
                            continue;
                        }
                    } catch (\Exception $e) {
                        Log::warning('Error processing text element: ' . $e->getMessage());
                        continue;
                    }
                }
                $text .= "\n";
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Text) {
                $text .= $element->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Table) {
                foreach ($element->getRows() as $row) {
                    foreach ($row->getCells() as $cell) {
                        foreach ($cell->getElements() as $cellElement) {
                            $text .= $this->extractElementText($cellElement) . ' ';
                        }
                    }
                    $text .= "\n"; // Add line break after each row
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Link) {
                $text .= $element->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\ListItem) {
                $text .= $element->getTextObject()->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Footnote) {
                foreach ($element->getElements() as $footnoteElement) {
                    $text .= $this->extractElementText($footnoteElement) . ' ';
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Image) {
                // Skip images gracefully - they don't contain extractable text
                Log::info('Skipping image element during text extraction');
                return '';
            }
        } catch (\Exception $e) {
            Log::warning('Error processing element: ' . $e->getMessage());
            return '';
        }

        return $text;
    }

    /**
     * Safely extract text from PhpWord document, skipping problematic elements
     *
     * @param \PhpOffice\PhpWord\PhpWord $phpWord
     * @return string
     */
    private function extractTextFromWordSafely($phpWord)
    {
        $text = '';

        try {
            foreach ($phpWord->getSections() as $section) {
                foreach ($section->getElements() as $element) {
                    try {
                        $text .= $this->extractElementText($element) . ' ';
                    } catch (\Exception $e) {
                        Log::warning('Skipping problematic element: ' . $e->getMessage());
                        continue;
                    }
                }
                $text .= "\n";
            }
        } catch (\Exception $e) {
            Log::error('Safe text extraction failed: ' . $e->getMessage());
        }

        return trim($text);
    }

    public function downloadResume($id)
    {
        $resume = Resume::findOrFail($id);

        if ($resume) {
            if ($resume->resume_file) {
                // Get file content from S3
                $fileContent = Storage::disk('s3')->get($resume->resume_file);

                // Create a response with the file content
                // Determine content type based on file extension
                $extension = pathinfo($resume->resume_file_name, PATHINFO_EXTENSION);
                $contentType = 'application/octet-stream'; // Default content type

                // Set content type based on common file extensions
                if (strtolower($extension) === 'pdf') {
                    $contentType = 'application/pdf';
                } elseif (in_array(strtolower($extension), ['doc', 'docx'])) {
                    $contentType = 'application/msword';
                }

                $safeFilename = basename(str_replace(['/', '\\'], '_', $resume->resume_file_name));


                return response($fileContent, 200, [
                    'Content-Type' => $contentType,
                    'Content-Disposition' => 'attachment; filename="' . $safeFilename . '"',
                ]);
            }
        }
    }
}
