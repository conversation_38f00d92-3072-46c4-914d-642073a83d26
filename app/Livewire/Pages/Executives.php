<?php

namespace App\Livewire\Pages;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class Executives extends Component
{
    use WithPagination;

    public $search = '';
    public $sortField = 'name';
    public $sortDirection = 'asc';

    // Modal states
    public $showModal = false;
    public $editMode = false;
    public $executiveId = null;

    // Form fields
    public $name = '';
    public $email = '';
    public $phone_number = '';
    public $password = '';
    public $password_confirmation = '';
    public $executive_role = '';
    public $points = 0;
    public $view_points_deduction = 0;
    public $download_points_deduction = 0;
    public $ars_points_deduction = 0;
    public $export_points_deduction = 0;

    protected $queryString = [
        'search' => ['except' => ''],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    protected function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255',
                       $this->executiveId
                           ? 'unique:users,email,'.$this->executiveId
                           : 'unique:users,email'],
            'phone_number' => ['nullable', 'string', 'max:10', 'min:10',
                       $this->executiveId
                           ? 'unique:users,phone_number,'.$this->executiveId
                           : 'unique:users,phone_number'],
            'password' => $this->executiveId
                ? ['nullable', 'string', 'min:6', 'confirmed']
                : ['required', 'string', 'min:6', 'confirmed'],
            'executive_role' => ['required', 'string', 'in:Partner,Recruitment,Ed Tech,Marketing'],
            'points' => ['required', 'numeric', 'min:0'],
            'view_points_deduction' => ['required', 'numeric', 'min:0'],
            'download_points_deduction' => ['required', 'numeric', 'min:0'],
            'ars_points_deduction' => ['required', 'numeric', 'min:0'],
            'export_points_deduction' => ['required', 'numeric', 'min:0'],
        ];
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function openModal($id = null)
    {
        $this->resetValidation();
        $this->reset(['name', 'email', 'phone_number', 'password', 'password_confirmation', 'executive_role', 'points', 'view_points_deduction', 'download_points_deduction', 'ars_points_deduction', 'export_points_deduction']);

        if ($id) {
            $this->editMode = true;
            $this->executiveId = $id;
            $executive = User::findOrFail($id);
            $this->name = $executive->name;
            $this->email = $executive->email;
            $this->phone_number = $executive->phone_number;
            $this->executive_role = $executive->executive_role;
            $this->points = $executive->points;
            $this->view_points_deduction = $executive->view_points_deduction;
            $this->download_points_deduction = $executive->download_points_deduction;
            $this->ars_points_deduction = $executive->ars_points_deduction;
            $this->export_points_deduction = $executive->export_points_deduction;
        } else {
            $this->editMode = false;
            $this->executiveId = null;
            $this->executive_role = 'Partner'; // Default value
            // random password
            $this->password = Str::random(8);
            $this->password_confirmation = $this->password;
        }

        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
    }

    public function save()
    {
        $validated = $this->validate();

        if ($this->executiveId) {
            $executive = User::findOrFail($this->executiveId);
            $executive->name = $validated['name'];
            $executive->email = $validated['email'];
            $executive->phone_number = $validated['phone_number'];
            $executive->executive_role = $validated['executive_role'];
            if ($validated['password']) {
                $executive->password = Hash::make($validated['password']);
            }
            $executive->points = $validated['points'];
            $executive->view_points_deduction = $validated['view_points_deduction'];
            $executive->download_points_deduction = $validated['download_points_deduction'];
            $executive->ars_points_deduction = $validated['ars_points_deduction'];
            $executive->export_points_deduction = $validated['export_points_deduction'];
            $executive->save();

            session()->flash('message', 'Executive updated successfully.');
        } else {
            User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone_number' => $validated['phone_number'],
                'password' => Hash::make($validated['password']),
                'points' => $validated['points'],
                'view_points_deduction' => $validated['view_points_deduction'],
                'download_points_deduction' => $validated['download_points_deduction'],
                'ars_points_deduction' => $validated['ars_points_deduction'],
                'export_points_deduction' => $validated['export_points_deduction'],
                'role' => 'executive',
                'executive_role' => $validated['executive_role'],
            ]);

            session()->flash('message', 'Executive created successfully.');
        }

        $this->closeModal();
    }

    public function deleteExecutive($id)
    {
        $executive = User::findOrFail($id);
        $executive->delete();

        session()->flash('message', 'Executive deleted successfully.');
    }

    public function render()
    {
        $executives = User::where('role', 'executive')
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%'.$this->search.'%')
                      ->orWhere('email', 'like', '%'.$this->search.'%')
                      ->orWhere('phone_number', 'like', '%'.$this->search.'%');
                });
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(50);

        return view('livewire.pages.executives', [
            'executives' => $executives
        ]);
    }
}
