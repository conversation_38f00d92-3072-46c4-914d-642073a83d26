<?php

namespace App\Livewire\Pages;

use App\Models\Resume;
use App\Livewire\Traits\ResumeFilters;
use Flux\Flux;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;

class TrashResumes extends Component
{
    use WithPagination, WithFileUploads, ResumeFilters;

    public $search = '';
    public $minExperienceYears = null;
    public $minExperienceMonths = null;
    public $maxExperienceYears = null;
    public $maxExperienceMonths = null;
    public $minSalaryLacs = null;
    public $minSalaryThousands = null;
    public $maxSalaryLacs = null;
    public $maxSalaryThousands = null;
    public $location = '';
    public $noticePeriod = '';
    public $startDate = null;
    public $endDate = null;
    public $selectedResumes = [];

    public function mount()
    {
        $this->resetFilters();
    }

    public function resetFilters()
    {
        $this->reset([
            'search',
            'minExperienceYears',
            'minExperienceMonths',
            'maxExperienceYears',
            'maxExperienceMonths',
            'minSalaryLacs',
            'minSalaryThousands',
            'maxSalaryLacs',
            'maxSalaryThousands',
            'location',
            'noticePeriod',
            'startDate',
            'endDate',
            'selectedResumes'
        ]);
    }

    public function applyFilters()
    {
        $this->dispatch('filters-applied');
    }

    public function restoreResume($id)
    {
        $resume = Resume::withTrashed()->findOrFail($id);

        if ($resume) {
            $resume->restore();

            $this->dispatch(
                'notify',
                message: 'Resume restored successfully!',
                type: 'success'
            );
        }
    }

    public function forceDeleteResume($id)
    {
        $resume = Resume::withTrashed()->findOrFail($id);

        if ($resume) {
            if ($resume->resume_file) {
                Storage::disk('public')->delete($resume->resume_file);
            }
            $resume->forceDelete();

            $this->dispatch(
                'notify',
                message: 'Resume permanently deleted!',
                type: 'success'
            );
        }
    }

    public function confirmRestoreSelectedResumes()
    {
        if (empty($this->selectedResumes)) {
            return;
        }

        // show modal
        Flux::modal('confirm-restore-selected-resumes')->show();
    }

    public function restoreSelectedResumes()
    {
        if (empty($this->selectedResumes)) {
            return;
        }

        Resume::withTrashed()->whereIn('id', $this->selectedResumes)->restore();
        $this->selectedResumes = [];
        $this->dispatch(
            'notify',
            message: 'Resumes restored successfully!',
            type: 'success'
        );
        Flux::modal('confirm-restore-selected-resumes')->close();
    }

    public function confirmForceDeleteSelectedResumes()
    {
        if (empty($this->selectedResumes)) {
            return;
        }

        // show modal
        Flux::modal('confirm-force-delete-selected-resumes')->show();
    }

    public function forceDeleteSelectedResumes()
    {
        if (empty($this->selectedResumes)) {
            return;
        }

        $resumes = Resume::withTrashed()->whereIn('id', $this->selectedResumes)->get();

        foreach ($resumes as $resume) {
            if ($resume->resume_file) {
                Storage::disk('public')->delete($resume->resume_file);
            }
            $resume->forceDelete();
        }

        $this->selectedResumes = [];
        $this->dispatch(
            'notify',
            message: 'Resumes permanently deleted!',
            type: 'success'
        );
        Flux::modal('confirm-force-delete-selected-resumes')->close();
    }


    /**
     * Highlight search terms in text
     *
     * @param string|array $text The text to search in
     * @return string Text with highlighted search terms
     */
    public function highlightSearchTerm($text)
    {
        if (empty($this->search) || empty($text)) {
            return $text;
        }

        // Handle array input (like skills)
        if (is_array($text)) {
            return $text; // Return as is, we handle arrays separately
        }

        // Split search terms by comma and trim whitespace
        $searchTerms = array_map('trim', explode(',', $this->search));

        // Process each search term - highlight all terms found
        foreach ($searchTerms as $term) {
            if (empty($term)) continue;

            $quotedTerm = preg_quote($term, '/');
            $text = preg_replace('/(' . $quotedTerm . ')/i', '<span class="bg-yellow-200 dark:bg-yellow-700">$1</span>', $text);
        }

        return $text;
    }

    /**
     * Highlight search terms in an array of skills
     *
     * @param array $skills The skills array to highlight search terms in
     * @return array Array of skills with highlighted search terms
     */
    public function highlightSkills($skills)
    {
        if (empty($this->search) || empty($skills) || !is_array($skills)) {
            return $skills;
        }

        // Split search terms by comma and trim whitespace
        $searchTerms = array_map('trim', explode(',', $this->search));
        $highlightedSkills = $skills;

        foreach ($skills as $index => $skill) {
            $highlightedSkill = $skill;

            // Highlight all search terms found in this skill
            foreach ($searchTerms as $term) {
                if (empty($term)) continue;

                $quotedTerm = preg_quote($term, '/');
                $highlightedSkill = preg_replace('/(' . $quotedTerm . ')/i', '<span class="bg-yellow-200 dark:bg-yellow-700">$1</span>', $highlightedSkill);
            }

            $highlightedSkills[$index] = $highlightedSkill;
        }

        return $highlightedSkills;
    }


    protected $listeners = [
        'city-selected' => 'handleCitySelected'
    ];

    public function handleCitySelected($data)
    {
        if ($data['field'] === 'location') {
            $this->location = $data['city'];
        }
    }

    public function render()
    {
        $user = Auth::user();
        $query = Resume::onlyTrashed();

        // Apply all filters using the reusable function (excluding archive filters since this is trash)
        $this->applyFiltersToQuery($query, false);

        if ($this->search) {
            $this->dispatch('updatedSearch', search: $this->search);
        }

        $resumes = $query->orderBy('created_at', 'desc')->paginate(100);

        $role = $user->role;

        return view('livewire.pages.trash-resumes', [
            'resumes' => $resumes,
            'role' => $role,
        ]);
    }
}
