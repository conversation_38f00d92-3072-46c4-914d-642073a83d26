<?php

namespace App\Livewire\Pages;

use App\Models\Resume;
use App\Models\ResumeUnlock;
use App\Models\ArsRanking;
use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class Dashboard extends Component
{
    public function render()
    {
        $user = Auth::user();
        $data = [];

        if ($user->role == 'admin') {
            // Total Resumes on AWS
            $data['totalResumes'] = Resume::count();

            // Today's New Resumes
            $data['todayNewResumes'] = Resume::whereDate('created_at', today())->count();

            // New Resumes this Month
            $data['monthlyNewResumes'] = Resume::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count();

            // Today's Resume Downloads
            $data['todayDownloads'] = ResumeUnlock::where('downloaded', true)
                ->whereDate('updated_at', today())
                ->count();

            // Monthly Resume Downloads
            $data['monthlyDownloads'] = ResumeUnlock::where('downloaded', true)
                ->whereMonth('updated_at', now()->month)
                ->whereYear('updated_at', now()->year)
                ->count();

            // Today's Contact Views
            $data['todayViews'] = ResumeUnlock::where('viewed', true)
                ->whereDate('updated_at', today())
                ->count();

            // Monthly Contact Views
            $data['monthlyViews'] = ResumeUnlock::where('viewed', true)
                ->whereMonth('updated_at', now()->month)
                ->whereYear('updated_at', now()->year)
                ->count();

            // ARS Today
            $data['todayArs'] = ArsRanking::whereDate('created_at', today())->count();

            // ARS Monthly
            $data['monthlyArs'] = ArsRanking::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count();

            // Active/Inactive Users
            $data['activeUsers'] = User::where('role', 'executive')
                ->where('points', '>', 0)
                ->count();

            $data['inactiveUsers'] = User::where('role', 'executive')
                ->where('points', '<=', 0)
                ->count();
        } else if ($user->role == 'executive') {

            if ($user->executive_role == 'Partner') {
                $data['todayNewResumes'] = Resume::whereDate('created_at', today())->where('partner_id', $user->id)->count();

                // New Resumes this Month
                $data['monthlyNewResumes'] = Resume::whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->where('partner_id', $user->id)
                    ->count();
            } else {
                // Today's New Resumes (unlocked by this executive today)
                $data['todayNewResumes'] = $user->unlockedResumes()
                    ->whereDate('created_at', today())
                    ->count();

                // New Resumes this Month (unlocked by this executive this month)
                $data['monthlyNewResumes'] = $user->unlockedResumes()->whereMonth('created_at', now()->month)->whereYear('created_at', now()->year)->count();
            }

            // Total Points
            $data['totalPoints'] = $user->points;


            $data['totalViewed'] = $user->unlockedResumes()
                ->where('viewed', true)
                ->count();

            // Today's Views
            $data['todayViews'] = $user->unlockedResumes()
                ->where('viewed', true)
                ->whereDate('updated_at', today())
                ->count();

            // Monthly Views
            $data['monthlyViews'] = $user->unlockedResumes()
                ->where('viewed', true)
                ->whereMonth('updated_at', now()->month)
                ->whereYear('updated_at', now()->year)
                ->count();

            // Total Downloaded Resumes
            $data['totalDownloaded'] = $user->unlockedResumes()
                ->where('downloaded', true)
                ->count();

            // Today's Downloads
            $data['todayDownloads'] = $user->unlockedResumes()
                ->where('downloaded', true)
                ->whereDate('updated_at', today())
                ->count();

            // Monthly Downloads
            $data['monthlyDownloads'] = $user->unlockedResumes()
                ->where('downloaded', true)
                ->whereMonth('updated_at', now()->month)
                ->whereYear('updated_at', now()->year)
                ->count();

            // Conversion Rate (percentage of viewed resumes that were downloaded)
            $data['conversionRate'] = $data['totalViewed'] > 0
                ? round(($data['totalDownloaded'] / $data['totalViewed']) * 100)
                : 0;

            // ARS Usage
            $data['totalArs'] = $user->arsRankings()->count();

            // ARS Today
            $data['todayArs'] = $user->arsRankings()
                ->whereDate('created_at', today())
                ->count();

            // ARS Monthly
            $data['monthlyArs'] = $user->arsRankings()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count();
        }

        return view('livewire.pages.dashboard', ['data' => $data]);
    }
}
