<?php

namespace App\Livewire\Pages;

use App\Models\JobDescription;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ApplicationRanking extends Component
{
    use WithPagination;

    public $search = '';
    public $showAddModal = false;
    public $title = '';
    public $description = '';
    public $jobDescriptionIdToDelete = null;
    public $showDeleteConfirmModal = false;

    protected $queryString = [
        'search' => ['except' => ''],
    ];

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'required|string',
    ];

    public function mount()
    {
        // Check if user is executive
        if (Auth::user()->role !== 'executive') {
            return redirect()->route('dashboard');
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function openAddModal()
    {
        $this->reset(['title', 'description']);
        $this->showAddModal = true;
    }

    public function closeAddModal()
    {
        $this->showAddModal = false;
    }

    public function saveJobDescription()
    {
        $this->validate();

        JobDescription::create([
            'user_id' => Auth::id(),
            'title' => $this->title,
            'description' => $this->description,
        ]);

        $this->reset(['title', 'description']);
        $this->closeAddModal();

        $this->dispatch(
            'notify',
            message: 'Job description added successfully!',
            type: 'success'
        );
    }

    public function confirmDelete($id)
    {
        $this->jobDescriptionIdToDelete = $id;
        $this->showDeleteConfirmModal = true;
    }

    public function closeDeleteConfirmModal()
    {
        $this->showDeleteConfirmModal = false;
    }

    public function deleteJobDescription()
    {
        $jobDescription = JobDescription::where('id', $this->jobDescriptionIdToDelete)
            ->where('user_id', Auth::id())
            ->first();

        if ($jobDescription) {
            $jobDescription->delete();

            $this->dispatch(
                'notify',
                message: 'Job description deleted successfully!',
                type: 'success'
            );
        } else {
            $this->dispatch(
                'notify',
                message: 'Job description not found or you do not have permission to delete it.',
                type: 'error'
            );
        }

        $this->closeDeleteConfirmModal();
    }

    public function render()
    {
        $jobDescriptions = JobDescription::where('user_id', Auth::id())
            ->when($this->search, function ($query) {
                return $query->where(function ($query) {
                    $query->where('title', 'like', '%' . $this->search . '%')
                        ->orWhere('description', 'like', '%' . $this->search . '%');
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('livewire.pages.application-ranking', [
            'jobDescriptions' => $jobDescriptions
        ]);
    }
}
