<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ArsRanking extends Model
{
    protected $fillable = [
        'user_id',
        'resume_id',
        'job_description_id',
        'score',
        'feedback',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function resume()
    {
        return $this->belongsTo(Resume::class);
    }

    public function jobDescription()
    {
        return $this->belongsTo(JobDescription::class);
    }
}
