<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ResumeAtsScore extends Model
{
    use HasUuids;

    protected $fillable = [
        'resume_id',
        'resume_score',
        'ats_friendly_status',
        'resume_feedback',
        'suggestions_for_improvement',
        'status',
    ];

    /**
     * Get the resume that owns the ATS score.
     */
    public function resume()
    {
        return $this->belongsTo(Resume::class);
    }

    /**
     * Check if the ATS score has been parsed (status is 'parsed').
     */
    public function isParsed()
    {
        return $this->status === 'parsed';
    }
}
