<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Resume extends Model
{
    use SoftDeletes;
    protected $appends = ['resume_file_url'];

    protected $fillable = [
        'candidate_name',
        'phone_number',
        'email',
        'skills',
        'total_experience_years',
        'total_experience_months',
        'current_company',
        'designation',
        'current_location',
        'notice_period',
        'resume_file',
        'resume_file_name',
        'country_code',
        'qualification',
        'preferred_location',
        'current_salary_lacs',
        'current_salary_thousands',
        'expected_salary_lacs',
        'expected_salary_thousands',
        'partner_name',
        'partner_id',
        'is_archived',
        'resume_content',
        'home_town',
        'relevant_experience_years',
        'relevant_experience_months',
        'comments',
        'submitted_from',
    ];

    protected $casts = [
        'skills' => 'array',
        'is_archived' => 'boolean',
        'deleted_at' => 'datetime'
    ];

    public function getResumeFileUrlAttribute()
    {
        if (!$this->resume_file) {
            return null;
        }

        return config('filesystems.default') === 's3'
            ? Storage::disk('s3')->url($this->resume_file)
            : \Illuminate\Support\Facades\URL::to('storage/' . $this->resume_file);
    }

    public function arsRankings()
    {
        return $this->hasMany(ArsRanking::class);
    }

    public function latestArsRanking()
    {
        return $this->hasOne(ArsRanking::class)->latest();
    }

    public function partner()
    {
        return $this->belongsTo(User::class, 'partner_id', 'id');
    }

    public function atsScore()
    {
        return $this->hasOne(ResumeAtsScore::class);
    }
}
