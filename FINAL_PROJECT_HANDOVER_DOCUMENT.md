# Final Project Handover Document
## CareerFlute Application

---

## 📋 Project Overview

**Project Name:** CareerFlute  
**Purpose:** AI-powered resume management and candidate search platform  
**Target Users:** HR professionals, recruitment agencies, and partners  
**Core Functionality:** Parse resumes using ChatGPT API, manage candidate database, search/filter candidates, and rank applicants against job descriptions  

---

## 🛠️ Tech Stack

### Backend Framework
- **<PERSON><PERSON> 12** - PHP web application framework
- **Livewire** - Full-stack framework for dynamic interfaces
- **Flux UI** - Modern UI component library for Livewire
- **Tailwind CSS** - Utility-first CSS framework

### Database
- **MySQL** - Primary database
- **Eloquent ORM** - Database abstraction layer

### External Services
- **OpenAI ChatGPT API** - Resume parsing and applicant ranking
- **AWS S3** - File storage for resume documents
- **Resend** - Email service provider

### Key Dependencies
- **PhpOffice/PhpWord** - Word document processing
- **Smalot/PdfParser** - PDF text extraction
- **Maatwebsite/Excel** - Excel import/export functionality
- **League/Flysystem-AWS-S3** - AWS S3 integration

---

## 🏗️ Application Architecture

### Layout Structure
```
├── Sidebar Navigation (Desktop/Mobile responsive)
├── Header with user menu and settings
├── Main Content Area
└── Modals and overlays for actions
```

### User Roles & Access Control
1. **Admin** - Full system access, manage executives, view all statistics
2. **Executive** - Limited access based on role (Partner/Recruitment/Ed Tech/Marketing)
3. **Partner** - Can create resumes, view own submissions

### Middleware Protection
- `AdminMiddleware` - Admin-only routes
- `AdminPartnerMiddleware` - Admin and Partner access
- Standard Laravel auth middleware

---

## 📊 Database Models & Relationships

### Core Models

#### 1. User Model
- **Purpose:** Manages system users (admins, executives, partners)
- **Key Fields:** name, email, role, executive_role, points, deduction settings
- **Relationships:** hasMany(ResumeUnlock, JobDescription, ArsRanking)

#### 2. Resume Model
- **Purpose:** Stores candidate resume data and parsed information
- **Key Fields:** candidate details, experience, salary, skills, location, partner_id
- **Features:** Soft deletes, file URL generation, skills as JSON array
- **Relationships:** belongsTo(User as partner), hasMany(ArsRanking)

#### 3. JobDescription Model
- **Purpose:** Stores job descriptions for ARS (Applicant Ranking System)
- **Key Fields:** user_id, title, description
- **Relationships:** belongsTo(User), hasMany(ArsRanking)

#### 4. ArsRanking Model
- **Purpose:** Stores AI-generated rankings of resumes against job descriptions
- **Key Fields:** user_id, resume_id, job_description_id, score (1-10), feedback
- **Features:** Unique constraint prevents duplicate rankings

#### 5. ResumeUnlock Model
- **Purpose:** Tracks resume views and downloads with points deduction
- **Key Fields:** user_id, resume_id, viewed, downloaded
- **Features:** Unique constraint per user-resume pair

#### 6. City Model
- **Purpose:** Reference data for location filtering
- **Key Fields:** name
- **Features:** No timestamps, used for autocomplete

---

## 🧩 Main Components & Functionality

### 1. Dashboard Component (`app/Livewire/Pages/Dashboard.php`)
- **Purpose:** Shows system statistics and metrics
- **Admin View:** Total resumes, daily/monthly stats, user activity, ARS usage
- **Executive View:** Personal stats (unlocks, views, downloads, ARS usage)

### 2. Resumes Component (`app/Livewire/Pages/Resumes.php`)
- **Purpose:** Main resume management interface with advanced filtering
- **Actions:** View, edit, archive, delete, bulk operations, CSV export, ZIP download
- **Filters:** Experience, salary, location, notice period, date range, resume type/category
- **Features:** Search highlighting, pagination, points-based access control

### 3. SearchCandidates Component (`app/Livewire/SearchCandidates.php`)
- **Purpose:** Public search interface for finding candidates
- **Features:** Same filtering as Resumes, optimized queries, export functionality
- **Access Control:** Points deduction for viewing/downloading resumes

### 4. CreateResume Component (`app/Livewire/Pages/CreateResume.php`)
- **Purpose:** Add/edit resume with AI parsing
- **Features:** File upload, ChatGPT parsing, manual editing, partner assignment
- **File Support:** PDF, DOC, DOCX with text extraction
- **Caching:** File hash-based caching to avoid re-parsing same documents

### 5. ApplicationRanking Component (`app/Livewire/Pages/ApplicationRanking.php`)
- **Purpose:** Manage job descriptions for ARS system
- **Features:** CRUD operations, search, pagination
- **Access:** Executive users only

### 6. Executives Component (`app/Livewire/Pages/Executives.php`)
- **Purpose:** User management for admin
- **Features:** Create/edit executives, role assignment, points management
- **Roles:** Partner, Recruitment, Ed Tech & Marketing

### 7. TrashResumes Component (`app/Livewire/Pages/TrashResumes.php`)
- **Purpose:** Manage soft-deleted resumes
- **Features:** Restore, permanent delete, same filtering as main resumes

---

## 🔧 Key Features & Business Logic

### Resume Parsing System
- **AI Integration:** ChatGPT API extracts structured data from resume files
- **Fallback Strategies:** Multiple parsing attempts with different prompts
- **Caching:** MD5 hash-based caching prevents duplicate API calls
- **Error Handling:** Comprehensive error logging and user feedback

### Points System
- **View Points:** Deducted when viewing candidate contact details
- **Download Points:** Deducted when downloading resume files
- **ARS Points:** Deducted when using Applicant Ranking System
- **Export Points:** Deducted for bulk exports

### Filtering System
- **Reusable Filters:** `ResumeFilters` trait provides consistent filtering across components
- **Filter Types:** 
  - Filter 1: All/Open/Closed status
  - Filter 2: Resume Writing/Partner/Job Seekers/Duplicate detection
- **Advanced Search:** Comma-separated terms treated as AND conditions
- **Performance:** Database indexes on frequently searched columns

### File Management
- **Storage:** AWS S3 for production, local storage for development
- **File Types:** PDF, DOC, DOCX support with text extraction
- **Security:** Secure file URLs with proper access control

### Applicant Ranking System (ARS)
- **AI Comparison:** OpenAI API compares resumes to job descriptions
- **Scoring:** 1-10 scale with detailed feedback
- **Auto-cleanup:** Rankings automatically deleted after one month
- **UI Features:** Star display, feedback tooltips, copy functionality

---

## � API Endpoints

### ResumeParserController API Routes
All API endpoints are rate-limited to 60 requests per minute and grouped under `/api/` prefix:

#### 1. `POST /api/parse-resume`
- **Purpose:** Parse uploaded resume file using ChatGPT API
- **Input:** Resume file (PDF, DOC, DOCX)
- **Output:** Structured JSON data with candidate information
- **Rate Limit:** 60 requests/minute
- **Caching:** Uses MD5 file hash to cache results and avoid duplicate API calls

#### 2. `POST /api/save-resume`
- **Purpose:** Save parsed resume data to database
- **Input:** Resume file + parsed/manual data + partner information
- **Features:** Creates new resume or updates existing (by email), auto-creates partner users
- **Validation:** Comprehensive field validation with custom rules

#### 3. `POST /api/cities`
- **Purpose:** Get cities for location autocomplete
- **Input:** Search term
- **Output:** Filtered list of cities from database
- **Usage:** Used in location filters and form fields

#### 4. `POST /api/partners`
- **Purpose:** Get partners for dropdown selection
- **Input:** Search term (optional)
- **Output:** List of partner users with executive role
- **Usage:** Used in resume creation for partner assignment

---

## 🤖 AI Integration & Prompts

### Resume Parsing Prompt (ChatGPT API)
The system uses a structured prompt to extract candidate information from resume text:

```
Act as a professional resume parser. Extract structured information from the provided resume text and return a clean JSON object with exactly these keys:
{
 "candidate_name": "Full name of the candidate (Title Case or null)",
 "phone_number": "Phone number or null",
 "email": "Email address (lowercase or null)",
 "skills": [List of all skills, including technical and soft skills, mentioned anywhere in the resume or empty array],
 "total_experience_years": "Total years of experience (numeric or null)",
 "current_company": "Current employer name or null",
 "designation": "Current job title or null",
 "current_location": "Current city or location or null",
 "qualification": "Highest degree and specialization or null"
}

Guidelines:
• Output only the JSON object with no explanation, no markdown, no code blocks.
• If a field is missing or not explicitly mentioned, return null.
• candidate_name should be Title Case.
• email should be lowercase.
• skills should be an array of individual strings.
• Do not guess or infer missing information. Use only explicit text.

Resume Text (3000 chars):
[RESUME_CONTENT]
```

### ARS (Applicant Ranking System) Prompt
For comparing resumes against job descriptions:

```
You are an expert HR professional tasked with evaluating how well a candidate's resume matches a job description.

Please analyze the following resume and job description, then provide:
1. A score from 1 to 10 (where 10 is a perfect match)
2. A brief explanation of your rating (maximum 500 words) with pointers

Focus on skills match, experience relevance, and overall fit.

JOB DESCRIPTION:
{$jdContent}

RESUME:
{$resumeContent}

Respond with a JSON object containing:
{
    "score": [number between 1-10],
    "feedback": [explanation text]
}
```

### AI Response Handling
- **Multiple Fallback Strategies:** System tries different parsing approaches if initial attempt fails
- **UTF-8 Encoding:** Handles special characters and encoding issues
- **Markdown Cleanup:** Removes code block formatting from AI responses
- **Error Logging:** Comprehensive logging for debugging failed parsing attempts

---

## 🔍 Search Functionality (SearchCandidates Component)

### Search Query Implementation
The SearchCandidates component provides powerful search capabilities across multiple resume fields:

#### Searchable Fields
- **candidate_name** - Full name search
- **email** - Email address search
- **phone_number** - Phone number search
- **skills** - JSON array search for technical and soft skills
- **current_company** - Current employer search
- **designation** - Job title search
- **current_location** - Location search
- **qualification** - Education/degree search
- **resume_content** - Full resume text search (conditionally excluded for performance)

#### Search Logic
```php
// Multi-field search with LIKE queries
$query->where('candidate_name', 'LIKE', $likeTerm)
      ->orWhere('email', 'LIKE', $likeTerm)
      ->orWhere('phone_number', 'LIKE', $likeTerm)
      ->orWhere('skills', 'LIKE', $likeTerm)
      ->orWhere('current_location', 'LIKE', $likeTerm)
      ->orWhere('preferred_location', 'LIKE', $likeTerm)
      ->orWhere('qualification', 'LIKE', $likeTerm)
      ->orWhere('total_experience_years', 'LIKE', $likeTerm)
      ->orWhere('current_salary_lacs', 'LIKE', $likeTerm)
      ->orWhere('expected_salary_lacs', 'LIKE', $likeTerm)
      ->orWhere('notice_period', 'LIKE', $likeTerm)
      ->orWhere('partner_name', 'LIKE', $likeTerm)
      ->orWhere('home_town', 'LIKE', $likeTerm)
      ->orWhere('relevant_experience_years', 'LIKE', $likeTerm)
      ->orWhere('current_company', 'LIKE', $likeTerm)
      ->orWhere('comments', 'LIKE', $likeTerm)
      ->orWhere('designation', 'LIKE', $likeTerm);

  // Only search resume_content if it's selected in the query
  // This is determined by checking if the field is available
  $columns = $subQuery->getQuery()->columns ?? ['*'];
  if (in_array('*', $columns) || in_array('resume_content', $columns)) {
      $subQuery->orWhere('resume_content', 'LIKE', $likeTerm);
  }
```

#### Search Features
- **Comma-separated AND Logic:** "PHP, Laravel" searches for resumes containing both terms
- **Real-time Search:** Live search with debouncing for better UX
- **Search Highlighting:** Matched terms highlighted in yellow in results
- **Performance Optimization:**
  - Database indexes on frequently searched columns
  - Conditional exclusion of large `resume_content` field
  - Selective field fetching for better performance

#### Filter Integration
- **Combined Filtering:** Search works alongside all filter options (experience, salary, location, etc.)
- **Role-based Results:** Partner users see only their own submissions
- **Archive Status:** Respects archive filter settings
- **Duplicate Detection:** Special filters for identifying duplicate profiles by email/phone

#### Performance Optimizations
- **Database Indexes:** Added on `candidate_name`, `email`, `phone_number`, `current_company`, `designation`, `current_location`
- **Query Optimization:** Uses LIKE instead of REGEXP for better performance
- **Conditional Fields:** Excludes `resume_content` from queries when not needed for search
- **Pagination:** Results paginated at 100 records per page

---

## �📁 Directory Structure

```
app/
├── Http/
│   ├── Controllers/Api/ResumeParserController.php
│   └── Middleware/ (AdminMiddleware, AdminPartnerMiddleware)
├── Livewire/
│   ├── Pages/ (Dashboard, Resumes, CreateResume, etc.)
│   ├── Components/ (ArsButton, ArsFilterButton)
│   ├── Auth/ (Login, Register, etc.)
│   └── Settings/ (Profile, Password, Appearance)
├── Models/ (User, Resume, JobDescription, etc.)
├── Services/ArsService.php
└── Traits/ResumeFilters.php

resources/views/
├── components/layouts/ (app, auth layouts)
├── livewire/ (component views)
└── partials/

routes/
├── web.php (main application routes)
├── api.php (API endpoints)
└── auth.php (authentication routes)

database/
├── migrations/ (database schema)
└── seeders/ (sample data)
```

---

## 🚀 Deployment & Configuration

### Environment Variables
- `OPENAI_API_KEY` - ChatGPT API access
- `AWS_*` - S3 storage configuration
- `RESEND_API_KEY` - Email service
- Database connection settings

### Key Artisan Commands
- `php artisan migrate` - Run database migrations
- `php artisan storage:link` - Link storage for file access
- `php artisan db:seed` - Seed initial data

### Performance Optimizations
- Database indexes on search columns
- Selective field fetching for large datasets
- Query optimization for duplicate detection
- File hash caching for parsed data

---

## 🔐 Security Features

- Role-based access control
- Middleware protection for sensitive routes
- File upload validation and security
- Points-based access limiting
- Soft deletes for data recovery
- Secure file URL generation