<div>
    <flux:breadcrumbs>
        <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>
            {{ __('Dashboard') }}
        </flux:breadcrumbs.item>

        <flux:breadcrumbs.item :href="route('search-candidates')" wire:navigate>
            {{ __('Resumes') }}
        </flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <!-- Filters Section -->
    <div class="mt-6 rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">



        <div class="flex items-center justify-between mb-6">
            <h2 class="text-base font-semibold text-zinc-900 dark:text-zinc-100">Filters</h2>
            <div class="flex items-center gap-3">
                @if ($executive_role === 'Partner')
                    <flux:switch wire:model.live="showCareerFluteResumes" class="w-full shrink-0 whitespace-nowrap"
                        label="Career Flute Resumes" />
                @endif
                <flux:button variant="outline" wire:click="resetFilters" icon="arrow-path-rounded-square">
                    Reset
                    Filters</flux:button>
            </div>
        </div>

        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">

            <!-- Date Range -->
            @if ($role === 'admin')
                <div class="grid grid-cols-2 gap-4">
                    <flux:input type="date" wire:model="startDate" placeholder="Start Date">
                        <x-slot:label>From</x-slot:label>
                    </flux:input>

                    <flux:input type="date" wire:model="endDate" placeholder="End Date">
                        <x-slot:label>To</x-slot:label>
                    </flux:input>
                </div>
            @endif
            <!-- Search -->
            <div class="md:col-span-2">

                <flux:input wire:keydown.enter="applyFilters" :label="$role === 'executive' ? 'Search' : ''"
                    wire:model="search" placeholder="Search by name, skills..." icon="magnifying-glass">
                    <x-slot:label>Search</x-slot:label>
                </flux:input>
            </div>


            <!-- Filter 1: Archive Status -->
            @if ($role == 'admin' || ($executive_role == 'Partner' && $showCareerFluteResumes == false))
                <div>
                    <label class="block mb-2 text-sm font-medium">Resume Status</label>

                    <flux:select wire:model="resumeType">
                        <option value="all">All Resumes</option>
                        <option value="not_archived">Open</option>
                        <option value="archived">Closed</option>
                    </flux:select>
                </div>
            @endif

            <!-- Filter 2: Resume Category -->
            @if ($role == 'admin')
                <div>
                    <label class="block mb-2 text-sm font-medium">Resume Category</label>

                    <flux:select wire:model="resumeCategory">
                        <option value="all">All Categories</option>
                        <option value="resume_writing">Resume Writing Resumes</option>
                        <option value="partner">Partner Resumes</option>
                        <option value="job_seeker">Job Seekers Resume</option>
                        <option value="duplicates_email">Duplicate Resumes by Email</option>
                        <option value="duplicates_phone">Duplicate Resumes by Phone Number</option>
                    </flux:select>
                </div>
            @endif



            <!-- Min Experience -->
            <div>
                <div class="grid grid-cols-2 gap-2">
                    <flux:select wire:model="minExperienceYears" label="Min Experience" placeholder="Years">
                        <option value="">Years</option>
                        @for ($i = 0; $i <= 30; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Year' : 'Years' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>

                    <flux:select wire:model="maxExperienceYears" label="Max Experience" placeholder="Years">
                        <option value="">Years</option>
                        @for ($i = 0; $i <= 30; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Year' : 'Years' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>
                </div>
            </div>



            <!-- Min Salary -->
            <div>
                <div class="grid grid-cols-2 gap-2">
                    <flux:select wire:model="minSalaryLacs" label="Min Salary" placeholder="Lacs">
                        <option value="">Lacs</option>
                        @for ($i = 0; $i <= 99; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Lac' : 'Lacs' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>

                    <flux:select wire:model="maxSalaryLacs" label="Max Salary" placeholder="Lacs">
                        <option value="">Lacs</option>
                        @for ($i = 0; $i <= 99; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Lac' : 'Lacs' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>
                </div>
            </div>

            <!-- Location with Indian Cities Search -->
            <div>
                <label class="block mb-2 text-sm font-medium">Location</label>

                @livewire('components.city-search', ['selectedCity' => $location, 'field' => 'location'])
            </div>

            <!-- Notice Period -->
            <div>
                <label class="block mb-2 text-sm font-medium">Notice Period</label>

                <flux:select wire:model="noticePeriod">
                    <option value="">Any</option>
                    <option value="immediate">Immediate Joiners</option>
                    <option value="0-15">15 Days</option>
                    <option value="0-30">1 Month</option>
                    <option value="0-60">2 Months</option>
                    <option value="0-90">3 Months</option>
                </flux:select>
            </div>

           
        </div>

        <!-- Apply Filters Button -->
        <div class="flex items-end justify-between w-full mt-3">
            <div class="w-full">
                @if ($role === 'executive' && $resumes && count($resumes) > 0)
                    <!-- ARS Button -->
                    <div class="flex items-end">
                        @livewire('components.ars-filter-button')
                    </div>
                @endif
            </div>
            <div class="flex justify-end items-center w-full gap-3">


                <flux:button wire:click="applyFilters" icon="funnel" class="w-full max-w-80">Apply Filters
                </flux:button>
            </div>
        </div>
    </div>



    <!-- Table Section -->
    <div class="mt-6 flow-root">

        <div class="flex justify-between mb-5">
            @if ($role === 'admin' || ($executive_role == 'Partner' && $showCareerFluteResumes == false))
                <!-- Archive Toggle Button -->
                <div class="flex items-center">
                    <!-- Archive Selected Button (visible only when items are selected) -->
                    @if (count($selectedResumes) > 0)
                        @if ($resumeType !== 'all')
                            <flux:tooltip
                                content="{{ $resumeType === 'archived' ? 'Unarchive Selected' : 'Archive Selected' }} ({{ count($selectedResumes) }})">
                                <flux:button variant="outline" wire:click="archiveSelectedResumes"
                                    class="ml-3 inline-flex items-center gap-1">
                                    <flux:icon
                                        name="{{ $resumeType === 'archived' ? 'archive-box-x-mark' : 'archive-box-arrow-down' }}"
                                        class="size-4" />
                                </flux:button>
                            </flux:tooltip>
                        @endif

                        {{-- <flux:tooltip content="Delete Selected ({{ count($selectedResumes) }})">
                            <flux:button variant="outline" wire:click="confirmDeleteSelectedResumes"
                                class="ml-3 inline-flex items-center gap-1">
                                <flux:icon name="trash" class="size-4" />
                            </flux:button>
                        </flux:tooltip> --}}
                    @endif
                </div>
            @endif

            <div class="flex space-x-3 ms-auto">
                @if ($role === 'admin')
                    <flux:tooltip content="Recycle Bin">
                        <flux:button variant="outline" href="{{ route('resumes.trash') }}" wire:navigate
                            class="inline-flex items-center gap-1">
                            <flux:icon name="recycle" class="size-4" />
                        </flux:button>
                    </flux:tooltip>
                @endif
                @if ($role === 'admin' || $showCareerFluteResumes == false)
                    <flux:tooltip
                        content="Export CSV {{ $role != 'admin' && $executive_role != 'Partner' && $export_points_deduction > 0 ? '(Per row: ' . $export_points_deduction . ' points deduction)' : '' }}">
                        <flux:button variant="outline" wire:click="downloadResumes"
                            class="inline-flex items-center cursor-pointer">
                            <img class="h-4 dark:invert-0 invert" src="{{ asset('images/download.png') }}"
                                alt="download">
                        </flux:button>
                    </flux:tooltip>
                @endif

                @if ($role === 'admin')
                    <flux:tooltip content="Download Resume Files (ZIP)">
                        <flux:button variant="outline" wire:click="downloadResumeFiles"
                            class="inline-flex items-center cursor-pointer">
                            <flux:icon name="archive-box-arrow-down" class="size-4" />
                        </flux:button>
                    </flux:tooltip>

                    <flux:tooltip content="Download CSV Template">
                        <flux:button variant="outline" wire:click="downloadTemplate"
                            class="inline-flex items-center gap-1 cursor-pointer">
                            <flux:icon name="document-arrow-down" class="size-4" />
                        </flux:button>
                    </flux:tooltip>

                    <div x-data="{ uploading: false }" x-on:csv-imported.window="uploading = false"
                        class="flex items-center">
                        <input type="file" wire:model="csvFile" id="csvFile" accept=".csv" class="hidden"
                            wire:loading.attr="disabled" x-on:change="uploading = true">
                        <flux:tooltip content="Import CSV">
                            <flux:button variant="outline" x-bind:disabled="uploading"
                                @click="document.getElementById('csvFile').click()"
                                class="inline-flex items-center gap-1 cursor-pointer">
                                <flux:icon name="document-arrow-up" class="size-4" x-show="!uploading" />
                                <flux:icon name="arrow-path" class="size-4 animate-spin" x-show="uploading" />
                            </flux:button>
                        </flux:tooltip>
                    </div>
                @endif

                @if ($role === 'admin')
                    <flux:tooltip content="Add Resume">
                        <flux:button variant="primary" href="{{ route('resumes.create') }}" wire:navigate
                            icon="plus">
                        </flux:button>
                    </flux:tooltip>
                @endif
            </div>
        </div>

        @if ($resumes && count($resumes) > 0)
            <!-- Add pagination links -->
            <div class="mt-6 px-4 py-3 border-t border-zinc-200 dark:border-zinc-700">
                {{ $resumes->links() }}
            </div>
        @endif

        <div class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
                <div
                    class="overflow-hidden rounded-xl border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
                    <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                        <thead class="bg-zinc-50 dark:bg-zinc-800">
                            <tr>
                                @if ($role === 'admin' || ($executive_role == 'Partner' && $showCareerFluteResumes == false))
                                    <!-- Select All Checkbox -->
                                    <th scope="col" class="relative px-4 py-3.5">
                                        <div class="flex items-center">
                                            <input type="checkbox"
                                                class="h-4 w-4 rounded border-zinc-300 text-primary-600 focus:ring-primary-600 dark:border-zinc-600 dark:accent-white dark:focus:ring-primary-500 accent-zinc-800"
                                                wire:model="$all"
                                                wire:click="$set('selectedResumes', $event.target.checked ? {{ $resumes->pluck('id') }} : [])">
                                        </div>
                                    </th>
                                    <th scope="col"
                                        class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 max-w-40">
                                        <div class="flex items-center gap-2">
                                            <flux:icon name="briefcase" class="size-4" />
                                            Source
                                        </div>
                                    </th>
                                @endif

                                <th scope="col"
                                    class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                    <div class="flex items-center gap-2">
                                        <flux:icon name="user" class="size-4" />
                                        Personal Info
                                    </div>
                                </th>
                                @if ($role == 'admin')
                                    <th scope="col"
                                        class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                        <div class="flex items-center gap-2">
                                            <flux:icon name="code-bracket" class="size-4" />
                                            Expertise
                                        </div>
                                    </th>
                                @endif
                                <th scope="col"
                                    class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                    <div class="flex items-center gap-2">
                                        <flux:icon name="briefcase" class="size-4" />
                                        Professional Details
                                    </div>
                                </th>
                                @if ($role != 'admin')
                                    <th scope="col"
                                        class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 ">
                                        <div class="flex items-center gap-2">
                                            <flux:icon name="code-bracket" class="size-4" />
                                            Skills
                                        </div>
                                    </th>
                                @endif

                                <th scope="col"
                                    class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                    <div class="flex items-center gap-2">
                                        <flux:icon name="briefcase" class="size-4" />
                                        Comments
                                    </div>
                                </th>

                                @if ($role === 'admin')
                                    <th scope="col"
                                        class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                        <div class="flex items-center gap-2">
                                            <flux:icon name="information-circle" class="size-4" />
                                            Info
                                        </div>
                                    </th>
                                @endif
                                <th scope="col"
                                    class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                    <div class="flex items-center gap-2">
                                        <flux:icon name="cog" class="size-4" />
                                        Actions
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-zinc-200 dark:divide-zinc-700">
                            @forelse($resumes as $resume)
                                <tr wire:key="resume-{{ $resume->id }}" class="group">
                                    @if ($role === 'admin' || ($executive_role == 'Partner' && $showCareerFluteResumes == false))
                                        <!-- Checkbox -->
                                        <td class="px-4 py-3">
                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                    class="h-4 w-4 rounded border-zinc-300 text-primary-600 focus:ring-primary-600 dark:border-zinc-600 dark:accent-white accent-accent dark:focus:ring-primary-500"
                                                    wire:model.live="selectedResumes" value="{{ $resume->id }}">
                                            </div>
                                        </td>

                                        <td class="px-4 py-3">
                                            {!! $this->highlightSearchTerm($resume->partner_name) !!}
                                        </td>
                                    @endif
                                    <!-- Basic Info -->
                                    <td class="px-4 py-3 text-xs space-y-2">

                                        <div>
                                            <div class="font-medium text-zinc-900 dark:text-zinc-100">Candidate Name
                                            </div>
                                            <div class="text-zinc-500 dark:text-zinc-400">
                                                {!! $this->highlightSearchTerm($resume->candidate_name) !!}</div>
                                        </div>
                                        <div>
                                            <div class="font-medium text-zinc-900 dark:text-zinc-100">Email
                                                ID</div>
                                            <div class="text-zinc-500 dark:text-zinc-400">
                                                @if ($role === 'executive' && !$resume->viewed && $partner_id != $resume->partner_id)
                                                    {{ substr($resume->email, 0, 2) }}*****{{ strstr($resume->email, '@') }}
                                                @else
                                                    {!! $this->highlightSearchTerm($resume->email) !!}
                                                @endif
                                            </div>
                                        </div>
                                        <div>
                                            <div class="font-medium text-zinc-900 dark:text-zinc-100">Phone
                                                Number</div>
                                            <div class="text-zinc-500 dark:text-zinc-400">
                                                @if ($role === 'executive' && !$resume->viewed && $partner_id != $resume->partner_id)
                                                    {{ $resume->country_code }}
                                                    ****{{ substr($resume->phone_number, -2) }}
                                                @else
                                                    {{ $resume->country_code }} {{ $resume->phone_number }}
                                                @endif
                                            </div>
                                        </div>

                                        @if ($resume->qualification)
                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Education
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    {!! $this->highlightSearchTerm($resume->qualification) !!}</div>
                                            </div>
                                        @endif

                                        @if ($role == 'admin')
                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Current
                                                    Location
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    {!! $this->highlightSearchTerm($resume->current_location) !!}</div>
                                            </div>
                                        @endif

                                        @if ($resume->home_town)
                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100"> Home Town
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    {!! $this->highlightSearchTerm($resume->home_town) !!}</div>
                                            </div>
                                        @endif
                                    </td>



                                    <!-- Expertise -->
                                    @if ($role == 'admin')
                                        <td class="px-4 py-3">
                                            @if ($resume->designation)
                                                <div>
                                                    <div class="font-medium text-zinc-900 dark:text-zinc-100 text-sm">
                                                        Role
                                                    </div>
                                                    <div class="text-zinc-500 dark:text-zinc-400 text-sm">
                                                        {!! $this->highlightSearchTerm($resume->designation) !!}</div>
                                                </div>
                                            @endif

                                            @if ($resume->skills)
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100 text-sm mt-3">
                                                    Skills
                                                </div>
                                                <div x-data="{
                                                    expanded: false,
                                                    skills: {{ json_encode($resume->skills) }},
                                                    searchTerm: '{{ $search }}'
                                                }" class="min-w-[12rem] mt-1">
                                                    <div class="flex flex-wrap gap-1">
                                                        @foreach ($resume->skills as $index => $skill)
                                                            <span wire:key="resume-skill-{{ $index }}"
                                                                x-show="expanded || {{ $index }} < 10"
                                                                class="inline-flex items-center rounded-md bg-zinc-50 px-1.5 py-0.5 text-xs font-medium text-zinc-700 ring-1 ring-inset ring-zinc-600/10 dark:bg-zinc-800 dark:text-zinc-300 dark:ring-zinc-600">
                                                                {!! $this->highlightSearchTerm($skill) !!}
                                                            </span>
                                                        @endforeach
                                                    </div>
                                                    <button x-show="skills.length > 10 && !expanded"
                                                        @click="expanded = true"
                                                        class="mt-1 text-xs font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                                        <span x-text="`+${skills.length - 10} more`"></span>
                                                    </button>
                                                    <button x-show="expanded" @click="expanded = false"
                                                        class="mt-1 text-xs font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                                        Show Less
                                                    </button>
                                                </div>
                                            @endif

                                        </td>
                                    @endif

                                    <!-- Professional Details -->
                                    <td class="px-4 py-3">
                                        <div class="space-y-2 text-xs">
                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Total
                                                    Experience
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    @if (str_contains($resume->total_experience_years, '+'))
                                                        {{ str_replace('+', '', $resume->total_experience_years) }}{{ $resume->total_experience_months ? '.' . $resume->total_experience_months : '' }}+
                                                        Years
                                                    @else
                                                        {{ $resume->total_experience_years }}{{ $resume->total_experience_months ? '.' . $resume->total_experience_months : '' }}
                                                        Years
                                                    @endif
                                                </div>
                                            </div>

                                            @if ($role == 'admin')
                                                <div>
                                                    <div class="font-medium text-zinc-900 dark:text-zinc-100">Relevant
                                                        Experience
                                                    </div>
                                                    <div class="text-zinc-500 dark:text-zinc-400">
                                                        @if (str_contains($resume->relevant_experience_years, '+'))
                                                            {{ str_replace('+', '', $resume->relevant_experience_years) }}{{ $resume->relevant_experience_months ? '.' . $resume->relevant_experience_months : '' }}+
                                                            Years
                                                        @else
                                                            {{ $resume->relevant_experience_years }}{{ $resume->relevant_experience_months ? '.' . $resume->relevant_experience_months : '' }}
                                                            Years
                                                        @endif
                                                    </div>
                                                </div>
                                            @endif

                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">
                                                    Current/Last
                                                    Company</div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    {!! $this->highlightSearchTerm($resume->current_company) !!}</div>
                                            </div>

                                            @if ($role != 'admin' && $resume->designation)
                                                <div>
                                                    <div class="font-medium text-zinc-900 dark:text-zinc-100">Role
                                                    </div>
                                                    <div class="text-zinc-500 dark:text-zinc-400">
                                                        {!! $this->highlightSearchTerm($resume->designation) !!}</div>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">
                                                    Current/Last
                                                    CTC</div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    ₹{{ $resume->current_salary_lacs }}{{ $resume->current_salary_thousands ? '.' . $resume->current_salary_thousands : '' }}
                                                    LPA</div>
                                            </div>
                                            @if ($role == 'admin')
                                                <div>
                                                    <div class="font-medium text-zinc-900 dark:text-zinc-100">Expected
                                                        CTC
                                                    </div>
                                                    <div class="text-zinc-500 dark:text-zinc-400">
                                                        ₹{{ $resume->expected_salary_lacs }}{{ $resume->expected_salary_thousands ? '.' . $resume->expected_salary_thousands : '' }}
                                                        LPA</div>
                                                </div>

                                                <div>
                                                    <div class="font-medium text-zinc-900 dark:text-zinc-100">Notice
                                                        Period
                                                    </div>
                                                    <div class="text-zinc-500 dark:text-zinc-400">
                                                        {{ $resume->notice_period }} days</div>
                                                </div>
                                            @else
                                                <div>
                                                    <div class="font-medium text-zinc-900 dark:text-zinc-100">Current
                                                        Location
                                                    </div>
                                                    <div class="text-zinc-500 dark:text-zinc-400">
                                                        {!! $this->highlightSearchTerm($resume->current_location) !!}</div>
                                                </div>
                                            @endif

                                        </div>
                                    </td>

                                    <!-- Expertise -->
                                    @if ($role != 'admin')
                                        <td class="px-4 py-3">
                                            @if ($resume->skills)
                                                <div x-data="{
                                                    expanded: false,
                                                    skills: {{ json_encode($resume->skills) }},
                                                    searchTerm: '{{ $search }}'
                                                }" class="min-w-[10rem] max-w-80 mt-1">
                                                    <div class="flex flex-wrap gap-1">
                                                        @foreach ($resume->skills as $index => $skill)
                                                            <span
                                                                wire:key="resume-executive-skill-{{ $index }}"
                                                                x-show="expanded || {{ $index }} < 10"
                                                                class="inline-flex items-center rounded-md bg-zinc-50 px-1.5 py-0.5 text-xs font-medium text-zinc-700 ring-1 ring-inset ring-zinc-600/10 dark:bg-zinc-800 dark:text-zinc-300 dark:ring-zinc-600">
                                                                {!! $this->highlightSearchTerm($skill) !!}
                                                            </span>
                                                        @endforeach
                                                    </div>
                                                    <button x-show="skills.length > 10 && !expanded"
                                                        @click="expanded = true"
                                                        class="mt-1 text-xs font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                                        <span x-text="`+${skills.length - 10} more`"></span>
                                                    </button>
                                                    <button x-show="expanded" @click="expanded = false"
                                                        class="mt-1 text-xs font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                                        Show Less
                                                    </button>
                                                </div>
                                            @endif

                                        </td>
                                    @endif
                                    <td class="px-4 py-3">
                                        <div class="space-y-2 text-xs">

                                            <div class="text-zinc-500 dark:text-zinc-400">
                                                {!! $this->highlightSearchTerm(\Illuminate\Support\Str::limit($resume->comments, 100)) !!}</div>
                                        </div>
                                    </td>

                                    @if ($role === 'admin')
                                        <td class="px-4 py-3">
                                            <div class="space-y-2 text-xs">
                                                <div>
                                                    <div
                                                        class="font-medium text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                                        Created At
                                                    </div>
                                                    <div class="text-zinc-500 dark:text-zinc-400 whitespace-nowrap">
                                                        {{ $resume->created_at?->format('d M, Y') }}</div>
                                                </div>
                                                <div>
                                                    <div class="font-medium text-zinc-900 dark:text-zinc-100">
                                                        Status
                                                    </div>
                                                    <div class="text-zinc-500 dark:text-zinc-400 mt-1">
                                                        @if ($resume->is_archived)
                                                            <flux:navlist.badge color="red">Closed
                                                            </flux:navlist.badge>
                                                        @else
                                                            <flux:navlist.badge color="green">Open
                                                            </flux:navlist.badge>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    @endif

                                    <!-- Actions -->
                                    <td>
                                        <div
                                            class="relative py-3 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 flex flex-col items-center justify-center gap-2 my-auto h-full">
                                            @if ($role == 'executive' && $partner_id != $resume->partner_id)
                                                @if (!$resume->viewed)
                                                    <flux:tooltip
                                                        content="{{ $viewCoins }} coins to view contact details">
                                                        <flux:button variant="outline"
                                                            wire:click="unlockResume({{ $resume->id }})"
                                                            wire:loading.attr="disabled"
                                                            class="inline-flex items-center gap-1 p-3" icon="eye">
                                                        </flux:button>
                                                    </flux:tooltip>
                                                @endif

                                                @if ($resume->resume_file)
                                                    @if (!$resume->downloaded)
                                                        <flux:tooltip
                                                            content="{{ $downloadCoins }} coins to unlock download">
                                                            <flux:button variant="outline" size="sm"
                                                                wire:click="processDownloadResume({{ $resume->id }})"
                                                                class="inline-flex items-center gap-1">
                                                                <img class="h-4 dark:invert-0 invert"
                                                                    src="{{ asset('images/download.png') }}"
                                                                    alt="download">
                                                            </flux:button>
                                                        </flux:tooltip>
                                                    @else
                                                        <flux:tooltip content="Download Resume">
                                                            <flux:button variant="outline" size="sm"
                                                                wire:click="processDownloadResume({{ $resume->id }})"
                                                                class="inline-flex items-center gap-1">
                                                                <img class="h-4 dark:invert-0 invert"
                                                                    src="{{ asset('images/download.png') }}"
                                                                    alt="download">
                                                            </flux:button>
                                                        </flux:tooltip>
                                                    @endif
                                                @endif

                                                <!-- ARS Button -->
                                                @if ($selectedJobDescriptionId)
                                                    <flux:tooltip content="Applicant Ranking System"
                                                        class="text-start">
                                                        @livewire(
                                                            'components.ars-button',
                                                            [
                                                                'resumeId' => $resume->id,
                                                                'jobDescriptionId' => $selectedJobDescriptionId,
                                                            ],
                                                            key('ars-button-' . $resume->id . '-' . $selectedJobDescriptionId)
                                                        )
                                                    </flux:tooltip>
                                                @endif
                                            @else
                                                @if ($resume->resume_file)
                                                    <flux:tooltip content="Download Resume">
                                                        <flux:button variant="outline" size="sm"
                                                            wire:click="processDownloadResume({{ $resume->id }})"
                                                            class="inline-flex items-center gap-1">
                                                            <img class="h-4 dark:invert-0 invert"
                                                                src="{{ asset('images/download.png') }}"
                                                                alt="download">
                                                        </flux:button>
                                                    </flux:tooltip>
                                                @endif

                                                @if ($partner_id == $resume->partner_id)
                                                    <!-- ARS Button -->
                                                    @if ($selectedJobDescriptionId)
                                                        <flux:tooltip content="Applicant Ranking System"
                                                            class="text-start">
                                                            @livewire(
                                                                'components.ars-button',
                                                                [
                                                                    'resumeId' => $resume->id,
                                                                    'jobDescriptionId' => $selectedJobDescriptionId,
                                                                ],
                                                                key('ars-button-' . $resume->id . '-' . $selectedJobDescriptionId)
                                                            )
                                                        </flux:tooltip>
                                                    @endif
                                                @endif

                                                @if ($role === 'admin')
                                                    <flux:tooltip content="Edit Resume">
                                                        <flux:button variant="outline" size="sm"
                                                            href="{{ route('resumes.edit', $resume) }}" wire:navigate
                                                            class="inline-flex items-center gap-1">
                                                            <flux:icon name="pencil" class="size-3" />
                                                        </flux:button>
                                                    </flux:tooltip>

                                                    <flux:tooltip content="Delete Resume">
                                                        <flux:button variant="outline" size="sm"
                                                            wire:confirm="Do you want to delete this resume?"
                                                            wire:click="deleteResume({{ $resume->id }})"
                                                            class="inline-flex items-center gap-1">
                                                            <flux:icon name="trash" class="size-3" />
                                                        </flux:button>
                                                    </flux:tooltip>
                                                @endif
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="9"
                                        class="px-4 py-8 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                        <div class="flex flex-col items-center justify-center">
                                            <flux:icon
                                                name="{{ $resumeType === 'archived' ? 'archive-box' : 'inbox' }}"
                                                class="size-12 mb-2 text-zinc-400 dark:text-zinc-500" />
                                            @if ($resumeType === 'archived')
                                                <p>No archived resumes found</p>
                                            @else
                                                <p>Search for candidates using filters</p>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


        @if ($resumes && count($resumes) > 0)
            <!-- Add pagination links -->
            <div class="mt-6 px-4 py-3 border-t border-zinc-200 dark:border-zinc-700">
                {{ $resumes->links() }}
            </div>
        @endif

    </div>

    <flux:modal name="confirm-delete-selected-resumes" class="md:w-96">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Delete Selected Resumes</flux:heading>
                <flux:text class="mt-2">Are you sure you want to delete the selected resumes?</flux:text>
            </div>
            <div class="flex">
                <flux:modal.close>
                    <flux:button type="submit" variant="outline">Cancel</flux:button>
                </flux:modal.close>
                <flux:spacer />
                <flux:button type="submit" variant="danger" wire:click="deleteSelectedResumes">Delete</flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Flash Message -->
    <div x-data="{
        show: false,
        message: '',
        type: '',
        showToast(message, type = 'success') {
            this.show = false;
            this.message = message;
            this.type = type;
            this.show = true;
            setTimeout(() => this.show = false, 3000);
        }
    }" @notify.window="showToast($event.detail.message, $event.detail.type)" x-show="show"
        x-transition.opacity.duration.300ms class="fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50"
        :class="type === 'error' ? 'bg-red-400 text-white' : 'bg-green-500 text-white'">
        <span x-text="message"></span>
    </div>

    @script
        <script>
            if (@js(session()->has('error'))) {
                $dispatch('notify', {
                    message: @js(session('error')),
                    type: 'error'
                });
            } else if (@js(session()->has('message'))) {
                $dispatch('notify', {
                    message: @js(session('message')),
                    type: 'success'
                });
            }
        </script>
    @endscript

    @push('scripts')
        @if ($role != 'admin')
            <script>
                // Disable text selection
                document.body.style.userSelect = 'none';
                document.body.style.webkitUserSelect = 'none';
                document.body.style.msUserSelect = 'none';
                document.body.style.mozUserSelect = 'none';

                // Disable right-click context menu
                document.addEventListener('contextmenu', e => e.preventDefault());

                // Disable keyboard shortcuts for inspect element
                document.addEventListener('keydown', function(e) {
                    // Disable F12
                    if (e.key === 'F12' || e.keyCode === 123) {
                        e.preventDefault();
                        return false;
                    }

                    // Disable Ctrl+Shift+I / Cmd+Option+I
                    if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'I' || e.key === 'i' || e.keyCode === 73)) {
                        e.preventDefault();
                        return false;
                    }

                    // Disable Ctrl+Shift+C / Cmd+Option+C
                    if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'C' || e.key === 'c' || e.keyCode === 67)) {
                        e.preventDefault();
                        return false;
                    }

                    // Disable Ctrl+Shift+J / Cmd+Option+J
                    if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'J' || e.key === 'j' || e.keyCode === 74)) {
                        e.preventDefault();
                        return false;
                    }

                    // Disable Ctrl+U / Cmd+U (view source)
                    if ((e.ctrlKey || e.metaKey) && (e.key === 'U' || e.key === 'u' || e.keyCode === 85)) {
                        e.preventDefault();
                        return false;
                    }
                });

                // Additional protection against devtools
                setInterval(function() {
                    debugger;
                }, 100);
            </script>
        @endif
    @endpush
</div>
