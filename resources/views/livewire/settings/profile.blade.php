<section class="w-full">
    @include('partials.settings-heading')

    @php
        $user = auth()->user();
        $disable = $user->role != 'admin' ? true : false;
    @endphp
    <x-settings.layout :heading="__('Profile')" :subheading="__('Update your name, email address and phone number')">
        <form wire:submit="updateProfileInformation" class="my-6 w-full space-y-6">
            <flux:input wire:model="name" :label="__('Name')" type="text" required autofocus autocomplete="name"
                :readonly="$disable" />

            <div>
                <flux:input wire:model="email" :label="__('Email')" type="email" required autocomplete="email"
                    :readonly="$disable" />


                @if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && !$user->hasVerifiedEmail())
                    <div>
                        <flux:text class="mt-4">
                            {{ __('Your email address is unverified.') }}

                            <flux:link class="text-sm cursor-pointer"
                                wire:click.prevent="resendVerificationNotification">
                                {{ __('Click here to re-send the verification email.') }}
                            </flux:link>
                        </flux:text>

                        @if (session('status') === 'verification-link-sent')
                            <flux:text class="mt-2 font-medium !dark:text-green-400 !text-green-600">
                                {{ __('A new verification link has been sent to your email address.') }}
                            </flux:text>
                        @endif
                    </div>
                @endif
            </div>

            <flux:input wire:model="phone_number" :label="__('Phone Number')" type="phone_number"
                autocomplete="phone_number" :readonly="$disable" />

            @if ($user->role === 'admin')
                <div class="flex items-center gap-4">
                    <div class="flex items-center justify-end">
                        <flux:button variant="primary" type="submit" class="w-full">{{ __('Save') }}</flux:button>
                    </div>

                    <x-action-message class="me-3" on="profile-updated">
                        {{ __('Saved.') }}
                    </x-action-message>
                </div>
            @endif
        </form>


        {{-- <livewire:settings.delete-user-form /> --}}
    </x-settings.layout>
</section>
