<div>
    @php
        $user = auth()->user();
    @endphp
    <flux:breadcrumbs>
        <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>
            {{ __('Dashboard') }}
        </flux:breadcrumbs.item>
    </flux:breadcrumbs>

    {{-- Stats Cards --}}
    <div class="grid gap-4 md:grid-cols-3 mt-10">
        {{-- Executive User Stats --}}
        @if ($user->role == 'executive')
            {{-- Total Points Card --}}
            @if ($user->executive_role != 'Partner')
                <div
                    class="md:col-span-3 rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Total Points</p>
                            <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                                {{ $data['totalPoints'] }}</h3>
                        </div>
                        <div class="flex md:flex-row flex-col items-center gap-3">
                            <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                                <flux:icon name="currency-dollar" class="size-5 text-zinc-500 dark:text-zinc-400" />
                            </div>
                            <flux:modal.trigger name="contact-admin">
                                <flux:button variant="primary">
                                    Get More Points
                                </flux:button>
                            </flux:modal.trigger>
                        </div>
                    </div>
                </div>
            @endif

            @if ($user->role == 'admin')
                {{-- Total Resumes Card --}}
                <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Total Resumes</p>
                            <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                                {{ $data['totalResumes'] }}</h3>
                        </div>
                        <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                            <flux:icon name="document-chart-bar" class="size-5 text-zinc-500 dark:text-zinc-400" />
                        </div>
                    </div>
                </div>
            @endif

            {{-- Today's New Resumes Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Today's New Resumes</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['todayNewResumes'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="document-arrow-down" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Monthly New Resumes Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">New Resumes this Month</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['monthlyNewResumes'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="calendar-days" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Total Viewed Resumes Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Total Viewed</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">{{ $data['totalViewed'] }}
                        </h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="eye" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Today's Views Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Today's Contact Views</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">{{ $data['todayViews'] }}
                        </h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="eye" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Monthly Views Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Monthly Contact Views</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['monthlyViews'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="eye" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Total Downloaded Resumes Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Total Downloads</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['totalDownloaded'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="arrow-down-on-square" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Today's Downloads Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Today's Downloads</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['todayDownloads'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="arrow-down-on-square" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Monthly Downloads Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Monthly Downloads</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['monthlyDownloads'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="arrow-down-on-square" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Conversion Rate Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Conversion Rate</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['conversionRate'] }}%</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="chart-bar" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Total ARS Usage Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Total ARS Usage</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">{{ $data['totalArs'] }}
                        </h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="star" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Today's ARS Usage Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">ARS Today</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">{{ $data['todayArs'] }}
                        </h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="star" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Monthly ARS Usage Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">ARS Monthly</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">{{ $data['monthlyArs'] }}
                        </h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="star" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>
        @endif

        {{-- Admin User Stats --}}
        @if ($user->role == 'admin')
            {{-- Total Resumes Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Total Resumes</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['totalResumes'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="document-chart-bar" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Today's New Resumes Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Today's New Resumes</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['todayNewResumes'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="document-arrow-down" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- New Resumes this Month Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">New Resumes this Month</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['monthlyNewResumes'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="calendar-days" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Today's Resume Downloads Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Today's Resume Downloads</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['todayDownloads'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="arrow-down-on-square" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Monthly Resume Downloads Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Monthly Resume Downloads</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['monthlyDownloads'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="arrow-down-on-square" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Today's Contact Views Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Today's Contact Views</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['todayViews'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="eye" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Monthly Contact Views Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Monthly Contact Views</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['monthlyViews'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="eye" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- ARS Today Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">ARS Today</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">{{ $data['todayArs'] }}
                        </h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="star" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- ARS Monthly Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">ARS Monthly</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['monthlyArs'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="star" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Active Users Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Active Users</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['activeUsers'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="user" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>

            {{-- Inactive Users Card --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Inactive Users</p>
                        <h3 class="mt-2 text-3xl font-semibold text-zinc-900 dark:text-white">
                            {{ $data['inactiveUsers'] }}</h3>
                    </div>
                    <div class="rounded-lg bg-zinc-100 p-2 dark:bg-zinc-800">
                        <flux:icon name="user-minus" class="size-5 text-zinc-500 dark:text-zinc-400" />
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
