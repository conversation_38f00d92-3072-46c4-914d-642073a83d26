<div>
    <flux:breadcrumbs>
        <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>
            {{ __('Dashboard') }}
        </flux:breadcrumbs.item>

        <flux:breadcrumbs.item :href="route('resumes')" wire:navigate>
            {{ __('Resumes') }}
        </flux:breadcrumbs.item>

        <flux:breadcrumbs.item :href="route('resumes.trash')" wire:navigate>
            {{ __('Trash') }}
        </flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <!-- Filters Section -->
    <div class="mt-6 rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-base font-semibold text-zinc-900 dark:text-zinc-100">Filters</h2>
            <flux:button variant="outline" size="sm" wire:click="resetFilters" icon="arrow-path-rounded-square">Reset
                Filters</flux:button>
        </div>

        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">

            <!-- Date Range -->
            @if ($role === 'admin')
                <div class="grid grid-cols-2 gap-4">
                    <flux:input type="date" wire:model="startDate" placeholder="Start Date">
                        <x-slot:label>From</x-slot:label>
                    </flux:input>

                    <flux:input type="date" wire:model="endDate" placeholder="End Date">
                        <x-slot:label>To</x-slot:label>
                    </flux:input>
                </div>
            @endif
            <!-- Search -->
            <div class="md:col-span-2">
                <flux:input wire:keydown.enter="applyFilters" wire:model="search"
                    placeholder="Search by name, skills..." icon="magnifying-glass">
                    <x-slot:label>Search</x-slot:label>
                </flux:input>
            </div>

            <!-- Min Experience -->
            <div>
                <label class="block mb-2 text-sm font-medium">Min Experience</label>
                <div class="grid grid-cols-2 gap-2">
                    <flux:select wire:model="minExperienceYears" placeholder="Years">
                        <option value="">Years</option>
                        @for ($i = 0; $i <= 30; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Year' : 'Years' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>

                    <flux:select wire:model="minExperienceMonths" placeholder="Months">
                        <option value="">Months</option>
                        @for ($i = 0; $i <= 11; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Month' : 'Months' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>
                </div>
            </div>

            <!-- Max Experience -->
            <div>
                <label class="block mb-2 text-sm font-medium">Max Experience</label>
                <div class="grid grid-cols-2 gap-2">
                    <flux:select wire:model="maxExperienceYears" placeholder="Years">
                        <option value="">Years</option>
                        @for ($i = 0; $i <= 30; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Year' : 'Years' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>

                    <flux:select wire:model="maxExperienceMonths" placeholder="Months">
                        <option value="">Months</option>
                        @for ($i = 0; $i <= 11; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Month' : 'Months' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>
                </div>
            </div>

            <!-- Min Salary -->
            <div>
                <label class="block mb-2 text-sm font-medium">Min Salary</label>
                <div class="grid grid-cols-2 gap-2">
                    <flux:select wire:model="minSalaryLacs" placeholder="Lacs">
                        <option value="">Lacs</option>
                        @for ($i = 0; $i <= 99; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Lac' : 'Lacs' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>

                    <flux:select wire:model="minSalaryThousands" placeholder="Thousands">
                        <option value="">Thousands</option>
                        @for ($i = 0; $i <= 99; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Thousand' : 'Thousands' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>
                </div>
            </div>

            <!-- Max Salary -->
            <div>
                <label class="block mb-2 text-sm font-medium">Max Salary</label>
                <div class="grid grid-cols-2 gap-2">
                    <flux:select wire:model="maxSalaryLacs" placeholder="Lacs">
                        <option value="">Lacs</option>
                        @for ($i = 0; $i <= 99; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Lac' : 'Lacs' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>

                    <flux:select wire:model="maxSalaryThousands" placeholder="Thousands">
                        <option value="">Thousands</option>
                        @for ($i = 0; $i <= 99; $i++)
                            <flux:select.option value="{{ $i }}">
                                {{ $i }} {{ $i == 1 ? 'Thousand' : 'Thousands' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>
                </div>
            </div>

            <!-- Location with Indian Cities Search -->
            <div>
                @livewire('components.city-search', ['selectedCity' => $location, 'field' => 'location'])
            </div>

            <!-- Notice Period -->
            <div>
                <label class="block mb-2 text-sm font-medium">Notice Period</label>
                <flux:select wire:model="noticePeriod">
                    <option value="">Any</option>
                    <option value="immediate">Immediate Joiners</option>
                    <option value="1-15">15 Days</option>
                    <option value="1-30">1 Month</option>
                    <option value="1-60">2 Months</option>
                    <option value="1-90">3 Months</option>
                </flux:select>
            </div>

            <!-- Apply Filters Button -->
            <div class="flex items-end">
                <flux:button wire:click="applyFilters" icon="funnel" class="w-full">Apply Filters</flux:button>
            </div>
        </div>
    </div>

    <!-- Table Section -->
    <div class="mt-6 flow-root">

        <div class="flex justify-between mb-5">
            @if ($role === 'admin')
                <!-- Restore/Delete Selected Buttons -->
                <div class="flex items-center">
                    <!-- Restore Selected Button (visible only when items are selected) -->
                    @if (count($selectedResumes) > 0)
                        <flux:tooltip content="Restore Selected ({{ count($selectedResumes) }})">
                            <flux:button variant="outline" wire:click="confirmRestoreSelectedResumes"
                                class="ml-3 inline-flex items-center gap-1">
                                <flux:icon name="arrow-uturn-left" class="size-4" />
                            </flux:button>
                        </flux:tooltip>

                        <flux:tooltip content="Permanently Delete Selected ({{ count($selectedResumes) }})">
                            <flux:button variant="outline" wire:click="confirmForceDeleteSelectedResumes"
                                class="ml-3 inline-flex items-center gap-1">
                                <flux:icon name="trash" class="size-4" />
                            </flux:button>
                        </flux:tooltip>
                    @endif
                </div>
            @endif

            @if ($role === 'admin')
                <div class="flex space-x-3 ms-auto">
                    <flux:tooltip content="Back to Resumes">
                        <flux:button variant="outline" href="{{ route('resumes') }}" wire:navigate
                            class="inline-flex items-center gap-1">
                            <flux:icon name="arrow-left" class="size-4" />
                            Back to Resumes
                        </flux:button>
                    </flux:tooltip>
                </div>
            @endif
        </div>

        @if ($resumes && count($resumes) > 0)
            <div class="mt-6 px-4 py-3 border-t border-zinc-200 dark:border-zinc-700">
                {{ $resumes->links() }}
            </div>
        @endif

        <div class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
                <div
                    class="overflow-hidden rounded-xl border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
                    <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                        <thead class="bg-zinc-50 dark:bg-zinc-800">
                            <tr>
                                @if ($role === 'admin')
                                    <!-- Select All Checkbox -->
                                    <th scope="col" class="relative px-4 py-3.5">
                                        <div class="flex items-center">
                                            <input type="checkbox"
                                                class="h-4 w-4 rounded border-zinc-300 text-primary-600 focus:ring-primary-600 dark:border-zinc-600 dark:accent-white dark:focus:ring-primary-500 accent-zinc-800"
                                                wire:model="$all"
                                                wire:click="$set('selectedResumes', $event.target.checked ? {{ $resumes->pluck('id') }} : [])">
                                        </div>
                                    </th>
                                    <th scope="col"
                                        class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                        <div class="flex items-center gap-2">
                                            <flux:icon name="briefcase" class="size-4" />
                                            Partner
                                        </div>
                                    </th>
                                @endif

                                <th scope="col"
                                    class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                    <div class="flex items-center gap-2">
                                        <flux:icon name="user" class="size-4" />
                                        Personal Info
                                    </div>
                                </th>
                                <th scope="col"
                                    class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                    <div class="flex items-center gap-2">
                                        <flux:icon name="user-circle" class="size-4" />
                                        Role
                                    </div>
                                </th>
                                <th scope="col"
                                    class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                    <div class="flex items-center gap-2">
                                        <flux:icon name="code-bracket" class="size-4" />
                                        Skills
                                    </div>
                                </th>
                                <th scope="col"
                                    class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                    <div class="flex items-center gap-2">
                                        <flux:icon name="briefcase" class="size-4" />
                                        Professional Details
                                    </div>
                                </th>

                                <th scope="col"
                                    class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                    <div class="flex items-center gap-2">
                                        <flux:icon name="briefcase" class="size-4" />
                                        Comments
                                    </div>
                                </th>

                                @if ($role === 'admin')
                                    <th scope="col"
                                        class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                        <div class="flex items-center gap-2">
                                            <flux:icon name="information-circle" class="size-4" />
                                            Info
                                        </div>
                                    </th>
                                @endif
                                <th scope="col"
                                    class="whitespace-nowrap px-4 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                                    <div class="flex items-center gap-2">
                                        <flux:icon name="cog" class="size-4" />
                                        Actions
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-zinc-200 dark:divide-zinc-700">
                            @forelse($resumes as $resume)
                                <tr wire:key="resume-{{ $resume->id }}" class="group">
                                    @if ($role === 'admin')
                                        <!-- Checkbox -->
                                        <td class="px-4 py-3">
                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                    class="h-4 w-4 rounded border-zinc-300 text-primary-600 focus:ring-primary-600 dark:border-zinc-600 dark:accent-white accent-accent dark:focus:ring-primary-500"
                                                    wire:model.live="selectedResumes" value="{{ $resume->id }}">
                                            </div>
                                        </td>

                                        <td class="px-4 py-3">
                                            {!! $this->highlightSearchTerm($resume->partner_name) !!}
                                        </td>
                                    @endif
                                    <!-- Basic Info -->
                                    <td class="px-4 py-3 text-xs space-y-2">

                                        <div>
                                            <div class="font-medium text-zinc-900 dark:text-zinc-100">Candidate Name
                                            </div>
                                            <div class="text-zinc-500 dark:text-zinc-400">
                                                {!! $this->highlightSearchTerm($resume->candidate_name) !!}</div>
                                        </div>
                                        <div>
                                            <div class="font-medium text-zinc-900 dark:text-zinc-100">Email
                                                ID</div>
                                            <div class="text-zinc-500 dark:text-zinc-400">
                                                {!! $this->highlightSearchTerm($resume->email) !!}
                                            </div>
                                        </div>
                                        <div>
                                            <div class="font-medium text-zinc-900 dark:text-zinc-100">Phone
                                                Number</div>
                                            <div class="text-zinc-500 dark:text-zinc-400">
                                                {{ $resume->country_code }} {{ $resume->phone_number }}
                                            </div>
                                        </div>

                                        @if ($resume->current_location)
                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Education
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    {!! $this->highlightSearchTerm($resume->qualification) !!}</div>
                                            </div>
                                        @endif

                                        @if ($resume->current_location)
                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Current
                                                    Location
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    {!! $this->highlightSearchTerm($resume->current_location) !!}</div>
                                            </div>
                                        @endif

                                        @if ($resume->home_town)
                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100"> Home Town
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    {!! $this->highlightSearchTerm($resume->home_town) !!}</div>
                                            </div>
                                        @endif
                                    </td>


                                    <!-- Role -->
                                    <td class="px-4 py-3">
                                        <div class="text-xs">
                                            <div class="font-medium text-zinc-900 dark:text-zinc-100">
                                                {!! $this->highlightSearchTerm($resume->designation) !!}
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Skills -->
                                    <td class="px-4 py-3">
                                        <div x-data="{
                                            expanded: false,
                                            skills: {{ json_encode($resume->skills) }},
                                            searchTerm: '{{ $search }}',
                                            checkSearchMatch() {
                                                // Auto-expand if search term matches any skill
                                                if (this.searchTerm && this.skills.some(skill =>
                                                        skill.toLowerCase().includes(this.searchTerm.toLowerCase()))) {
                                                    this.expanded = true;
                                                }
                                            },
                                            init() {
                                                this.checkSearchMatch();
                                                // Listen for the updatedSearch event from Livewire
                                                window.addEventListener('updatedSearch', (event) => {
                                                    this.searchTerm = event.detail.search;
                                                    this.checkSearchMatch();
                                                });
                                            }
                                        }" class="min-w-[12rem]">
                                            <div class="flex flex-wrap gap-1">
                                                @foreach ($resume->skills as $index => $skill)
                                                    <span wire:key="resume-executive-skill-{{ $index }}"
                                                        x-show="expanded || {{ $index }} < 10 || (searchTerm && '{{ $skill }}'.toLowerCase().includes(searchTerm.toLowerCase()))"
                                                        class="inline-flex items-center rounded-md bg-zinc-50 px-1.5 py-0.5 text-xs font-medium text-zinc-700 ring-1 ring-inset ring-zinc-600/10 dark:bg-zinc-800 dark:text-zinc-300 dark:ring-zinc-600">
                                                        {!! $this->highlightSearchTerm($skill) !!}
                                                    </span>
                                                @endforeach
                                            </div>
                                            <button x-show="skills.length > 10 && !expanded"
                                                @click="expanded = true"
                                                class="mt-1 text-xs font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                                <span x-text="`+${skills.length - 10} more`"></span>
                                            </button>
                                            <button x-show="expanded" @click="expanded = false"
                                                class="mt-1 text-xs font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                                Show Less
                                            </button>
                                        </div>
                                    </td>

                                    <!-- Professional Details -->
                                    <td class="px-4 py-3">
                                        <div class="space-y-2 text-xs">
                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Total
                                                    Experience
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    @if (str_contains($resume->total_experience_years, '+'))
                                                        {{ str_replace('+', '', $resume->total_experience_years) }}{{ $resume->total_experience_months ? '.' . $resume->total_experience_months : '' }}+
                                                        Years
                                                    @else
                                                        {{ $resume->total_experience_years }}{{ $resume->total_experience_months ? '.' . $resume->total_experience_months : '' }}
                                                        Years
                                                    @endif
                                                </div>
                                            </div>

                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Relevant
                                                    Experience
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    @if (str_contains($resume->relevant_experience_years, '+'))
                                                        {{ str_replace('+', '', $resume->relevant_experience_years) }}{{ $resume->relevant_experience_months ? '.' . $resume->relevant_experience_months : '' }}+
                                                        Years
                                                    @else
                                                        {{ $resume->relevant_experience_years }}{{ $resume->relevant_experience_months ? '.' . $resume->relevant_experience_months : '' }}
                                                        Years
                                                    @endif
                                                </div>
                                            </div>

                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Current/Last
                                                    Company</div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    {!! $this->highlightSearchTerm($resume->current_company) !!}</div>
                                            </div>

                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Current/Last
                                                    CTC</div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    ₹{{ $resume->current_salary_lacs }}{{ $resume->current_salary_thousands ? '.' . $resume->current_salary_thousands : '' }}
                                                    LPA</div>
                                            </div>
                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Expected CTC
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    ₹{{ $resume->expected_salary_lacs }}{{ $resume->expected_salary_thousands ? '.' . $resume->expected_salary_thousands : '' }}
                                                    LPA</div>
                                            </div>

                                            <div>
                                                <div class="font-medium text-zinc-900 dark:text-zinc-100">Notice Period
                                                </div>
                                                <div class="text-zinc-500 dark:text-zinc-400">
                                                    {{ $resume->notice_period }} days</div>
                                            </div>
                                        </div>
                                    </td>



                                    <td class="px-4 py-3">
                                        <div class="space-y-2 text-xs">

                                            <div class="text-zinc-500 dark:text-zinc-400">
                                                {!! $this->highlightSearchTerm(\Illuminate\Support\Str::limit($resume->comments, 100)) !!}</div>
                                        </div>
                                    </td>

                                    @if ($role === 'admin')
                                        <td class="px-4 py-3">
                                            <div class="space-y-2 text-xs">
                                                <div>
                                                    <div
                                                        class="font-medium text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                                        Created At
                                                    </div>
                                                    <div class="text-zinc-500 dark:text-zinc-400 whitespace-nowrap">
                                                        {{ $resume->created_at?->format('d M, Y') }}</div>
                                                </div>
                                                <div>
                                                    <div
                                                        class="font-medium text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                                        Deleted At
                                                    </div>
                                                    <div class="text-zinc-500 dark:text-zinc-400 whitespace-nowrap">
                                                        {{ $resume->deleted_at?->format('d M, Y') }}</div>
                                                </div>
                                            </div>
                                        </td>
                                    @endif

                                    <!-- Actions -->
                                    <td>
                                        <div
                                            class="relative py-3 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 flex flex-col items-center justify-center gap-2 my-auto h-full">
                                            @if ($role === 'admin')
                                                <flux:tooltip content="Restore Resume">
                                                    <flux:button variant="outline" size="sm"
                                                        wire:click="restoreResume({{ $resume->id }})"
                                                        class="inline-flex items-center gap-1">
                                                        <flux:icon name="arrow-uturn-left" class="size-3" />
                                                    </flux:button>
                                                </flux:tooltip>

                                                <flux:tooltip content="Delete Permanently">
                                                    <flux:button variant="outline" size="sm"
                                                        wire:confirm="Do you want to permanently delete this resume? This action cannot be undone."
                                                        wire:click="forceDeleteResume({{ $resume->id }})"
                                                        class="inline-flex items-center gap-1">
                                                        <flux:icon name="trash" class="size-3" />
                                                    </flux:button>
                                                </flux:tooltip>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="9"
                                        class="px-4 py-8 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                        <div class="flex flex-col items-center justify-center">
                                            <flux:icon name="trash"
                                                class="size-12 mb-2 text-zinc-400 dark:text-zinc-500" />
                                            <p>No deleted resumes found</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


        @if ($resumes && count($resumes) > 0)
            <div class="mt-6 px-4 py-3 border-t border-zinc-200 dark:border-zinc-700">
                {{ $resumes->links() }}
            </div>
        @endif

    </div>

    <flux:modal name="confirm-restore-selected-resumes" class="md:w-96">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Restore Selected Resumes</flux:heading>
                <flux:text class="mt-2">Are you sure you want to restore the selected resumes?</flux:text>
            </div>
            <div class="flex">
                <flux:modal.close>
                    <flux:button type="submit" variant="outline">Cancel</flux:button>
                </flux:modal.close>
                <flux:spacer />
                <flux:button type="submit" variant="primary" wire:click="restoreSelectedResumes">Restore
                </flux:button>
            </div>
        </div>
    </flux:modal>

    <flux:modal name="confirm-force-delete-selected-resumes" class="md:w-96">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Permanently Delete Selected Resumes</flux:heading>
                <flux:text class="mt-2">Are you sure you want to permanently delete the selected resumes? This action
                    cannot be undone.</flux:text>
            </div>
            <div class="flex">
                <flux:modal.close>
                    <flux:button type="submit" variant="outline">Cancel</flux:button>
                </flux:modal.close>
                <flux:spacer />
                <flux:button type="submit" variant="danger" wire:click="forceDeleteSelectedResumes">Delete
                    Permanently</flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Flash Message -->
    <div x-data="{
        show: false,
        message: '',
        type: '',
        showToast(message, type = 'success') {
            this.show = false;
            this.message = message;
            this.type = type;
            this.show = true;
            setTimeout(() => this.show = false, 3000);
        }
    }" @notify.window="showToast($event.detail.message, $event.detail.type)" x-show="show"
        x-transition.opacity.duration.300ms class="fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg"
        :class="type === 'error' ? 'bg-red-400 text-white' : 'bg-green-500 text-white'">
        <span x-text="message"></span>
    </div>

    @script
        <script>
            if (@js(session()->has('error'))) {
                $dispatch('notify', {
                    message: @js(session('error')),
                    type: 'error'
                });
            } else if (@js(session()->has('message'))) {
                $dispatch('notify', {
                    message: @js(session('message')),
                    type: 'success'
                });
            }
        </script>
    @endscript
</div>
