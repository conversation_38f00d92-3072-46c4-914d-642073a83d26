<div>
    <flux:breadcrumbs>
        <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>
            {{ __('Dashboard') }}
        </flux:breadcrumbs.item>

        <flux:breadcrumbs.item :href="route('application-ranking')" wire:navigate>
            {{ __('Applicant Ranking System') }}
        </flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <div class="mt-5">
        <div class="sm:flex sm:items-center justify-between">
            <div class="sm:flex-auto">
                <h1 class="text-xl font-semibold text-zinc-900 dark:text-white">Job Descriptions</h1>
                <p class="mt-2 text-sm text-zinc-700 dark:text-zinc-300">
                    Manage your job descriptions for the Applicant Ranking System
                </p>
            </div>
            <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                <flux:button variant="primary" wire:click="openAddModal" icon="plus">
                    Add Job Description
                </flux:button>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="mt-6 mb-6 max-w-sm">
            <flux:input wire:model.live.debounce.300ms="search" placeholder="Search job descriptions..." icon="magnifying-glass">
            </flux:input>
        </div>

        <!-- Job Descriptions Grid -->
        <div class="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            @forelse ($jobDescriptions as $jobDescription)
                <div class="relative flex flex-col overflow-hidden rounded-lg border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-800">
                    <div class="flex flex-1 flex-col p-6">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-zinc-900 dark:text-white">
                                {{ $jobDescription->title }}
                            </h3>
                            {{-- <button wire:click="confirmDelete({{ $jobDescription->id }})" class="text-zinc-400 hover:text-red-500 dark:text-zinc-500 dark:hover:text-red-400">
                                <flux:icon name="trash" class="size-5" />
                            </button> --}}
                        </div>
                        <p class="mt-3 flex-grow text-sm text-zinc-600 dark:text-zinc-300" 
                           x-data="{ expanded: false }">
                            <span x-show="!expanded">
                                {{ Str::limit($jobDescription->description, 200) }}
                                @if(strlen($jobDescription->description) > 200)
                                    <button @click="expanded = true" 
                                            class="ml-1 text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium">
                                        Read more
                                    </button>
                                @endif
                            </span>
                            <span x-show="expanded" x-cloak>
                                {{ $jobDescription->description }}
                                <button @click="expanded = false" 
                                        class="ml-1 text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium">
                                    Show less
                                </button>
                            </span>
                        </p>
                        <div class="mt-4 text-xs text-zinc-500 dark:text-zinc-400">
                            {{ \Carbon\Carbon::parse($jobDescription->created_at)->format('d M Y, h:i A') }}
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full py-12 text-center">
                    <flux:icon name="document-text" class="mx-auto size-12 text-zinc-400" />
                    <h3 class="mt-2 text-sm font-semibold text-zinc-900 dark:text-white">No job descriptions</h3>
                    <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">
                        Get started by adding a new job description.
                    </p>
                    <div class="mt-6">
                        <flux:button variant="primary" wire:click="openAddModal" icon="plus">
                            Add Job Description
                        </flux:button>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if ($jobDescriptions->hasPages())
            <div class="mt-6 px-4 py-3 border-t border-zinc-200 dark:border-zinc-700">
                {{ $jobDescriptions->links() }}
            </div>
        @endif
    </div>

    <!-- Add Job Description Modal -->
    <flux:modal name="add-job-description-modal" wire:model="showAddModal" class="max-w-lg w-full">
        <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
            Add Job Description
        </h2>

        <div class="space-y-4 mb-4 mt-3">
            <div>
                <flux:input type="text" wire:model="title" placeholder="Enter job title">
                    <x-slot:label>Title</x-slot:label>
                </flux:input>
                @error('title') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
            </div>

            <div>
                <flux:textarea wire:model="description" rows="6" placeholder="Enter job description">
                    <x-slot:label>Description</x-slot:label>
                </flux:textarea>
                @error('description') <span class="text-sm text-red-600">{{ $message }}</span> @enderror
            </div>
        </div>

        <flux:button variant="outline" wire:click="closeAddModal">Cancel</flux:button>
        <flux:button wire:click="saveJobDescription" wire:loading.attr="disabled" class="ms-2">
            <span wire:loading.remove wire:target="saveJobDescription" variant="primary">Save</span>
            <span wire:loading wire:target="saveJobDescription">Saving...</span>
        </flux:button>
    </flux:modal>

    <!-- Delete Confirmation Modal -->
    <flux:modal name="delete-confirm-modal" wire:model="showDeleteConfirmModal" class="max-w-lg w-full">
        <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
            Delete Job Description
        </h2>

        <p class="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
            Are you sure you want to delete this job description? This action cannot be undone.
        </p>

        <flux:button variant="outline" wire:click="closeDeleteConfirmModal">Cancel</flux:button>
        <flux:button variant="danger" wire:click="deleteJobDescription" class="ms-2" wire:loading.attr="disabled">
            <span wire:loading.remove wire:target="deleteJobDescription">Delete</span>
            <span wire:loading wire:target="deleteJobDescription">Deleting...</span>
        </flux:button>
    </flux:modal>
</div>
