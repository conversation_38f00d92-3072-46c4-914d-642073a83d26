<div>
    <flux:breadcrumbs>
        <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>
            {{ __('Dashboard') }}
        </flux:breadcrumbs.item>

        <flux:breadcrumbs.item :href="route('resumes')" wire:navigate>
            {{ __('Resumes') }}
        </flux:breadcrumbs.item>

        <flux:breadcrumbs.item>
            {{ __('Add Resume') }}
        </flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <div class="mt-6">
        <div class="space-y-8">
            <!-- Resume Upload Section -->
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h2 class="text-base font-semibold text-zinc-900 dark:text-zinc-100">Resume Upload</h2>
                        <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">Upload Candidate's Resume (PDF, DOC,
                            DOCX)
                        </p>
                    </div>
                </div>

                <div x-data="{ uploading: false, progress: 0 }" x-on:livewire-upload-start="uploading = true"
                    x-on:livewire-upload-finish="uploading = false" x-on:livewire-upload-error="uploading = false"
                    x-on:livewire-upload-progress="progress = $event.detail.progress">

                    <div
                        class="flex justify-center rounded-lg border border-dashed border-zinc-300 px-6 py-10 dark:border-zinc-600">
                        <div class="text-center">
                            @if ($resume_file)
                                <div class="mb-4">
                                    <flux:icon name="document-check" class="mx-auto size-12 text-green-500" />
                                    <p class="mt-2 text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                        {{ $resume_file->getClientOriginalName() }}
                                    </p>
                                    <button type="button" wire:click="$set('resume_file', null)"
                                        class="mt-2 text-sm text-red-600 hover:text-red-500">
                                        Remove
                                    </button>
                                </div>
                            @else
                                <label for="resume_file">
                                    <flux:icon name="document-arrow-up" class="mx-auto size-12 text-zinc-400" />
                                    <div class="mt-4 flex text-sm leading-6 text-zinc-600 dark:text-zinc-400">
                                        <div
                                            class="relative cursor-pointer rounded-md font-semibold text-primary-600  hover:text-primary-500">
                                            <span>Click to Upload CV / Resume</span>
                                            <input id="resume_file" wire:model.live.debounce.300ms="resume_file"
                                                type="file" class="sr-only">
                                        </div>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                </label>
                            @endif

                            <!-- Progress Bar -->
                            <div x-show="uploading" class="mt-4">
                                <div
                                    class="h-fit w-full bg-zinc-200 rounded-full overflow-hidden dark:bg-zinc-700 text-xs">
                                    <div class="h-full bg-primary-600 rounded-full transition-all duration-300"
                                        :style="'width: ' + progress + '%'">
                                        Uploading...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @error('resume_file')
                        <span class="mt-2 text-sm text-red-600">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <!-- Partner Information -->
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <h2 class="text-base font-semibold text-zinc-900 dark:text-zinc-100 mb-6">Partner Information</h2>
                <div class="grid gap-6 mb-6 md:grid-cols-3">
                    <div>
                        <label class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-200">Partner Name</label>
                        <div>
                            @livewire('components.partner-search', ['selectedPartner' => $partner_name, 'field' => 'partner_name'])
                        </div>
                        @error('partner_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-200">Partner Email</label>
                        <flux:input wire:model.live.debounce.300ms="partner_email" type="email"
                            placeholder="Partner Email" :disabled="$partner_id !== null" />
                        @error('partner_email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-200">Partner Phone</label>
                        <flux:input wire:model.live.debounce.300ms="partner_phone_number" type="tel"
                            placeholder="Partner Phone Number" :disabled="$partner_id !== null" />
                        @error('partner_phone_number')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>



            <!-- Personal Information -->
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <h2 class="text-base font-semibold text-zinc-900 dark:text-zinc-100 mb-6">Personal Information
                </h2>
                <div class="grid gap-6 mb-6 md:grid-cols-3">
                    <flux:input wire:model.live.debounce.300ms="candidate_name" label="Candidate Name"
                        placeholder="Candidate Name as it appears in the Resume" required />

                    <flux:input wire:model.live.debounce.300ms="email" label="Candidate Email ID" type="email"
                        placeholder="Valid Email ID" required />


                    <flux:input wire:model.live.debounce.300ms="phone_number"
                        label="Candidate Phone
                            Number" type="tel"
                        placeholder="10 Digit Phone Number" required />


                    <flux:input wire:model.live.debounce.300ms="qualification" label="Educational Qualification"
                        placeholder="Highest Qualification" required />


                    <div>
                        <label class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-200">Current
                            Location</label>
                        <div>
                            @livewire('components.city-search', ['selectedCity' => $current_location, 'field' => 'current_location'])
                        </div>
                        @error('current_location')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-200">Home
                            Town</label>
                        <div>
                            @livewire('components.city-search', ['selectedCity' => $home_town, 'field' => 'home_town'])
                        </div>
                        @error('home_town')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                </div>
            </div>


            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <h2 class="text-base font-semibold text-zinc-900 dark:text-zinc-100 mb-6">Role
                </h2>
                <div class="grid gap-6 md:grid-cols-1">
                    <flux:input wire:model.live.debounce.300ms="designation" type="text"
                        placeholder="Role Mentioned in the Job Description" required />
                </div>

                @error('designation')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Skills -->
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <h2 class="text-base font-semibold text-zinc-900 dark:text-zinc-100 mb-6">Skills </h2>

                <div x-data="{
                    newSkill: '',
                    skills: @entangle('skills'),
                    errorMessage: '',
                    addSkill() {
                        if (this.newSkill.trim()) {
                            // Split by comma and process each skill
                            const skillsToAdd = this.newSkill.split(',').map(s => s.trim()).filter(s => s);
                            let duplicateFound = false;

                            // Add each skill if it doesn't already exist
                            skillsToAdd.forEach(skill => {
                                if (this.skills.includes(skill)) {
                                    this.errorMessage = `'${skill}' is already in your skills list`;
                                    duplicateFound = true;
                                    return;
                                }
                            });

                            if (!duplicateFound) {
                                skillsToAdd.forEach(skill => {
                                    this.skills.push(skill);
                                });
                                this.newSkill = '';
                                this.errorMessage = '';
                            }
                        }
                    },
                    removeSkill(skillToRemove) {
                        this.skills = this.skills.filter(skill => skill !== skillToRemove);
                        this.errorMessage = '';
                    }
                }">
                    <div class="space-y-4">
                        <!-- Input field for new skills -->
                        <div class="relative">
                            <input type="text" x-model.debounce.300ms="newSkill" @keydown.enter.prevent="addSkill()"
                                placeholder="Enter Skills (comma-separated) & Press Enter"
                                class="block w-full rounded-lg border border-zinc-300 px-3 py-2 text-sm placeholder-zinc-400 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 dark:border-zinc-600 dark:bg-zinc-700 dark:text-zinc-100 dark:placeholder-zinc-500">
                            <!-- Error message for duplicate skills -->
                            <p x-show="errorMessage" x-text="errorMessage" class="mt-2 text-sm text-red-600"></p>
                        </div>

                        <!-- Display selected skills -->
                        <div class="flex flex-wrap gap-2">
                            <template x-for="skill in skills" :key="skill">
                                <div
                                    class="inline-flex items-center gap-1 rounded-full bg-slate-100 px-3 py-1 text-sm font-medium text-primary-700 dark:bg-black dark:text-primary-300">
                                    <span x-text="skill"></span>
                                    <button type="button" @click="removeSkill(skill)"
                                        class="ml-1 text-primary-600 hover:text-primary-800 focus:outline-none dark:text-primary-400 dark:hover:text-primary-300">
                                        <flux:icon name="x-mark" class="size-4" />
                                    </button>
                                </div>
                            </template>
                        </div>

                        <!-- Empty state -->
                        <div x-show="skills.length === 0" class="text-sm text-zinc-500 dark:text-zinc-400">
                            No skills added yet. Start typing & hit enter to add your skills.
                        </div>
                    </div>
                </div>

                @error('skills')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Resume Content -->
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <h2 class="text-base font-semibold text-zinc-900 dark:text-zinc-100 mb-6">Copy & Paste Your Entire
                    Resume</h2>

                <flux:textarea wire:model.live.debounce.300ms="resume_content"
                    placeholder="Copy & Paste Your Entire Resume" />



                @error('resume_content')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror

            </div>



            <!-- Professional Information -->
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <h2 class="text-base font-semibold text-zinc-900 dark:text-zinc-100 mb-6">Professional Information</h2>
                <div class="grid gap-6 mb-6 md:grid-cols-3">

                    <!-- Total Experience -->
                    <div>
                        <label class="block mb-2 text-sm font-medium">Total Experience</label>
                        <div class="grid grid-cols-2 gap-2">
                            <flux:select wire:model.live="total_experience_years" placeholder="Years">
                                @for ($i = 0; $i <= 30; $i++)
                                    <flux:select.option value="{{ $i }}{{ $i == 30 ? '+' : '' }}">
                                        {{ $i }}{{ $i == 30 ? '+' : '' }}
                                        {{ $i == 1 ? 'Year' : 'Years' }}
                                    </flux:select.option>
                                @endfor
                            </flux:select>

                            <flux:select wire:model.live="total_experience_months" placeholder="Months">
                                @for ($i = 0; $i <= 11; $i++)
                                    <flux:select.option value="{{ $i }}">{{ $i }}
                                        {{ $i == 1 ? 'Month' : 'Months' }}
                                    </flux:select.option>
                                @endfor
                            </flux:select>
                        </div>
                    </div>

                    <!-- Relevant Experience -->
                    <div>
                        <label class="block mb-2 text-sm font-medium">Relevant Experience</label>
                        <div class="grid grid-cols-2 gap-2">
                            <flux:select wire:model.live="relevant_experience_years" placeholder="Years">
                                @for ($i = 0; $i <= 30; $i++)
                                    <flux:select.option value="{{ $i }}{{ $i == 30 ? '+' : '' }}">
                                        {{ $i }}{{ $i == 30 ? '+' : '' }}
                                        {{ $i == 1 ? 'Year' : 'Years' }}
                                    </flux:select.option>
                                @endfor
                            </flux:select>

                            <flux:select wire:model.live="relevant_experience_months" placeholder="Months">
                                @for ($i = 0; $i <= 11; $i++)
                                    <flux:select.option value="{{ $i }}">{{ $i }}
                                        {{ $i == 1 ? 'Month' : 'Months' }}
                                    </flux:select.option>
                                @endfor
                            </flux:select>
                        </div>
                    </div>

                    <!-- Current / Last Company -->
                    <flux:input wire:model.live="current_company" label="Current / Last Company"
                        placeholder="Name of the Current / Last Company" />
                    <div>
                        <label for="current_salary" class="block mb-2 text-sm font-medium">Current / Last CTC</label>
                        <div class="grid grid-cols-2 gap-2">
                            <!-- Current CTC -->
                            <flux:select wire:model.live="current_salary_lacs" placeholder="Lacs">
                                @for ($i = 0; $i <= 99; $i++)
                                    <flux:select.option value="{{ $i }}">{{ $i }}
                                        {{ $i == 1 ? 'Lac' : 'Lacs' }}
                                    </flux:select.option>
                                @endfor
                            </flux:select>

                            <flux:select wire:model.live="current_salary_thousands" placeholder="Thousands">
                                @for ($i = 0; $i <= 99; $i++)
                                    <flux:select.option value="{{ $i }}">{{ $i }}
                                        {{ $i == 1 ? 'Thousand' : 'Thousands' }}
                                    </flux:select.option>
                                @endfor
                            </flux:select>
                        </div>
                    </div>

                    <div>
                        <label for="expected_salary" class="block mb-2 text-sm font-medium">Expected CTC</label>
                        <div class="grid grid-cols-2 gap-2">
                            <!-- Expected CTC -->
                            <flux:select wire:model.live="expected_salary_lacs" placeholder="Lacs">
                                @for ($i = 0; $i <= 99; $i++)
                                    <flux:select.option value="{{ $i }}">{{ $i }}
                                        {{ $i == 1 ? 'Lac' : 'Lacs' }}
                                    </flux:select.option>
                                @endfor
                            </flux:select>

                            <flux:select wire:model.live="expected_salary_thousands" placeholder="Thousands">
                                @for ($i = 0; $i <= 99; $i++)
                                    <flux:select.option value="{{ $i }}">{{ $i }}
                                        {{ $i == 1 ? 'Thousand' : 'Thousands' }}
                                    </flux:select.option>
                                @endfor
                            </flux:select>
                        </div>
                    </div>

                    <!-- Notice Period -->
                    <flux:select wire:model.live="notice_period" label="Notice Period" placeholder="Days">
                        @for ($i = 0; $i <= 90; $i++)
                            <flux:select.option value="{{ $i }}">{{ $i }}
                                {{ $i == 1 ? 'Day' : 'Days' }}
                            </flux:select.option>
                        @endfor
                    </flux:select>

                </div>


            </div>



            {{-- comments textarea --}}
            <div class="rounded-xl border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                <h2 class="text-base font-semibold text-zinc-900 dark:text-zinc-100 mb-6">Comments</h2>
                <flux:textarea wire:model.live.debounce.300ms="comments" placeholder="Anything Noteworthy" />
                @error('comments')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4">
                <flux:button variant="outline" href="{{ route('resumes') }}" wire:navigate>
                    Cancel
                </flux:button>

                <flux:button wire:click="submit" variant="primary">
                    {{ $resumeId ? 'Update Resume' : 'Create Resume' }}
                </flux:button>
            </div>
        </div>
    </div>


    <!-- Flash Message -->
    <div x-data="{
        show: false,
        message: '',
        type: '',
        showToast(message, type = 'success') {
            this.show = false;
            this.message = message;
            this.type = type;
            this.show = true;
            setTimeout(() => this.show = false, 3000);
        }
    }" @notify.window="showToast($event.detail.message, $event.detail.type)" x-show="show"
        x-transition.opacity.duration.300ms class="fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg"
        :class="type === 'error' ? 'bg-red-400 text-white' : 'bg-green-500 text-white'">
        <span x-text="message"></span>
    </div>

    @script
        <script>
            if (@js(session()->has('error'))) {
                $dispatch('notify', {
                    message: @js(session('error')),
                    type: 'error'
                });
            } else if (@js(session()->has('message'))) {
                $dispatch('notify', {
                    message: @js(session('message')),
                    type: 'success'
                });
            }
        </script>
    @endscript
</div>
