<div>
    <flux:breadcrumbs>
        <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>
            {{ __('Dashboard') }}
        </flux:breadcrumbs.item>

        <flux:breadcrumbs.item :href="route('executives')" wire:navigate>
            {{ __('Executives') }}
        </flux:breadcrumbs.item>
    </flux:breadcrumbs>


    <div class="mt-5">
        <div class="sm:flex sm:items-center justify-between">
            <div class="sm:flex-auto">
                <p class="mt-2 text-sm text-zinc-700 dark:text-zinc-300">Manage executive users in the system</p>
            </div>
            <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                <flux:button variant="primary" wire:click="openModal">
                    Add Executive
                </flux:button>
            </div>
        </div>

        <div class="mt-4 flow-root">
            <div class="overflow-x-auto">
                <div class="inline-block min-w-full align-middle">
                    <!-- Search -->
                    <div class="mb-4 max-w-sm p-1">
                        <flux:input wire:model.live.debounce.300ms="search" placeholder="Search executives..."
                            icon="magnifying-glass">
                            <x-slot:label>Search</x-slot:label>
                        </flux:input>
                    </div>

                    @if ($executives && count($executives) > 0)
                        <!-- Add pagination links -->
                        <div class="mt-6 px-4 py-3 border-t border-zinc-200 dark:border-zinc-700">
                            {{ $executives->links() }}
                        </div>
                    @endif
                    <div
                        class="overflow-hidden rounded-xl border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
                        <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                            <thead class="bg-zinc-50 dark:bg-zinc-800">
                                <!-- Master heading row -->
                                <tr class="border-b border-zinc-200 dark:border-zinc-700">
                                    <th colspan="6"></th>
                                    <th colspan="4" class="text-center text-zinc-900 font-bold dark:text-zinc-100 py-3 bg-zinc-100 dark:bg-zinc-900">Points
                                        Deduction</th>
                                    <th></th>
                                </tr>

                                <!-- Actual headers row -->
                                <tr>
                                    <th scope="col"
                                        class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                        Initials
                                    </th>
                                    <th scope="col"
                                        class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                        <button wire:click="sortBy('name')" class="group inline-flex">
                                            Name
                                            <span class="ml-2 flex-none rounded text-zinc-400">
                                                @if ($sortField === 'name')
                                                    <flux:icon
                                                        name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}"
                                                        class="size-4" />
                                                @endif
                                            </span>
                                        </button>
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                        <button wire:click="sortBy('email')" class="group inline-flex">
                                            Email
                                            <span class="ml-2 flex-none rounded text-zinc-400">
                                                @if ($sortField === 'email')
                                                    <flux:icon
                                                        name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}"
                                                        class="size-4" />
                                                @endif
                                            </span>
                                        </button>
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                        <button wire:click="sortBy('phone_number')" class="group inline-flex">
                                            Phone Number
                                            <span class="ml-2 flex-none rounded text-zinc-400">
                                                @if ($sortField === 'phone_number')
                                                    <flux:icon
                                                        name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}"
                                                        class="size-4" />
                                                @endif
                                            </span>
                                        </button>
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                        <button wire:click="sortBy('executive_role')" class="group inline-flex">
                                            Role
                                            <span class="ml-2 flex-none rounded text-zinc-400">
                                                @if ($sortField === 'executive_role')
                                                    <flux:icon
                                                        name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}"
                                                        class="size-4" />
                                                @endif
                                            </span>
                                        </button>
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                        <button wire:click="sortBy('points')" class="group inline-flex">
                                            Points
                                            <span class="ml-2 flex-none rounded text-zinc-400">
                                                @if ($sortField === 'points')
                                                    <flux:icon
                                                        name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}"
                                                        class="size-4" />
                                                @endif
                                            </span>
                                        </button>
                                    </th>

                                    <th scope="col"
                                        class="px-3 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                        <button wire:click="sortBy('view_points_deduction')" class="group inline-flex">
                                            View 
                                            <span class="ml-2 flex-none rounded text-zinc-400">
                                                @if ($sortField === 'view_points_deduction')
                                                    <flux:icon
                                                        name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}"
                                                        class="size-4" />
                                                @endif
                                            </span>
                                        </button>
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                        <button wire:click="sortBy('download_points_deduction')"
                                            class="group inline-flex">
                                            Download
                                            <span class="ml-2 flex-none rounded text-zinc-400">
                                                @if ($sortField === 'download_points_deduction')
                                                    <flux:icon
                                                        name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}"
                                                        class="size-4" />
                                                @endif
                                            </span>
                                        </button>
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                        <button wire:click="sortBy('ars_points_deduction')" class="group inline-flex">
                                            ARS
                                            <span class="ml-2 flex-none rounded text-zinc-400">
                                                @if ($sortField === 'ars_points_deduction')
                                                    <flux:icon
                                                        name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}"
                                                        class="size-4" />
                                                @endif
                                            </span>
                                        </button>
                                    </th>
                                    <th scope="col"
                                        class="px-3 py-3.5 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100 whitespace-nowrap">
                                        <button wire:click="sortBy('export_points_deduction')"
                                            class="group inline-flex">
                                            Export
                                            <span class="ml-2 flex-none rounded text-zinc-400">
                                                @if ($sortField === 'export_points_deduction')
                                                    <flux:icon
                                                        name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}"
                                                        class="size-4" />
                                                @endif
                                            </span>
                                        </button>
                                    </th>
                                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                        <span class="sr-only">Actions</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-zinc-200 dark:divide-zinc-700">
                                @forelse($executives as $executive)
                                    <tr wire:key="{{ $executive->id }}">
                                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm">
                                            <div class="font-medium text-zinc-900 dark:text-zinc-100">
                                                {{ $executive->initials() }}</div>
                                        </td>
                                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm">
                                            <div class="font-medium text-zinc-900 dark:text-zinc-100">
                                                {{ $executive->name }}</div>
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-3 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                            {{ $executive->email }}
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-3 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                            {{ $executive->phone_number }}
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-3 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                            {{ $executive->executive_role }}
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-3 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                            {{ $executive->points }}
                                        </td>


                                        <td
                                            class="whitespace-nowrap px-3 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                            {{ $executive->view_points_deduction }}
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-3 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                            {{ $executive->download_points_deduction }}
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-3 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                            {{ $executive->ars_points_deduction }}
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-3 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                            {{ $executive->export_points_deduction }}
                                        </td>

                                        <td
                                            class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 space-x-2">
                                            <flux:button variant="outline" size="sm"
                                                wire:click="openModal({{ $executive->id }})"
                                                class="inline-flex items-center gap-1">
                                                <flux:icon name="pencil" class="size-4" />
                                            </flux:button>

                                            <flux:button variant="outline" size="sm"
                                                wire:confirm="Are you sure you want to delete this executive?"
                                                wire:click="deleteExecutive({{ $executive->id }})"
                                                class="inline-flex items-center gap-1">
                                                <flux:icon name="trash" class="size-4" />
                                            </flux:button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8"
                                            class="px-4 py-8 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                            <div class="flex flex-col items-center justify-center">
                                                <flux:icon name="users"
                                                    class="size-12 mb-2 text-zinc-400 dark:text-zinc-500" />
                                                <p>No executives found</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if ($executives && count($executives) > 0)
                        <!-- Add pagination links -->
                        <div class="mt-6 px-4 py-3 border-t border-zinc-200 dark:border-zinc-700">
                            {{ $executives->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <flux:modal wire:model="showModal" class="max-w-lg w-full">
        <form wire:submit="save">
            <h2 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">
                {{ $editMode ? 'Edit Executive' : 'Add Executive' }}
            </h2>

            <div class="space-y-4 mt-3">
                <flux:input wire:model="name" label="Name" placeholder="Enter full name" required />

                <flux:input wire:model="email" label="Email" type="email" placeholder="Enter email address"
                    required />
                <flux:input wire:model="phone_number" label="Phone Number" type="phone_number"
                    placeholder="Enter phone number" />

                <div class="relative">
                    <flux:input wire:model="password" label="Password" type="password" id="password-field"
                        placeholder="{{ $editMode ? 'Leave blank to keep current password' : 'Enter password' }}"
                        :required="!$editMode" />
                    <button type="button" onclick="togglePasswordVisibility('password-field')"
                        class="absolute right-2 top-8 text-zinc-500 hover:text-zinc-700 dark:hover:text-zinc-300">
                        <flux:icon name="eye" class="size-5" />
                    </button>
                </div>

                <div class="relative">
                    <flux:input wire:model="password_confirmation" label="Confirm Password" type="password"
                        id="confirm-password-field" placeholder="Confirm password" :required="!$editMode" />
                    <button type="button" onclick="togglePasswordVisibility('confirm-password-field')"
                        class="absolute right-2 top-8 text-zinc-500 hover:text-zinc-700 dark:hover:text-zinc-300">
                        <flux:icon name="eye" class="size-5" />
                    </button>
                </div>

                <script>
                    function togglePasswordVisibility(fieldId) {
                        const field = document.getElementById(fieldId);
                        field.type = field.type === 'password' ? 'text' : 'password';
                    }
                </script>

                <flux:select wire:model="executive_role" label="Role" required>
                    <option value="">Select Role</option>
                    <option value="Partner">Partner</option>
                    <option value="Recruitment">Recruitment</option>
                    <option value="Ed Tech">Ed Tech</option>
                    <option value="Marketing">Marketing</option>
                </flux:select>

                <flux:input wire:model="points" label="Points" type="number" min="0" step="0.1" required />

                <flux:input wire:model="view_points_deduction" label="View Points Deduction" type="number"
                    min="0" step="0.1" required />

                <flux:input wire:model="download_points_deduction" label="Download Points Deduction" type="number"
                    min="0" step="0.1" required />

                <flux:input wire:model="ars_points_deduction" label="ARS Points Deduction" type="number"
                    min="0" step="0.1" required />

                <flux:input wire:model="export_points_deduction" label="Export Points Deduction (Per row)"
                    type="number" min="0" step="0.1" required />
            </div>

            <div class="mt-4 flex justify-end space-x-2">
                <flux:button variant="outline" wire:click="closeModal">
                    Cancel
                </flux:button>
                <flux:button type="submit" variant="primary">
                    {{ $editMode ? 'Update' : 'Create' }}
                </flux:button>
            </div>
        </form>
    </flux:modal>

    <!-- Flash Message -->
    @if (session()->has('message'))
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 3000)"
            class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
            {{ session('message') }}
        </div>
    @endif
</div>
