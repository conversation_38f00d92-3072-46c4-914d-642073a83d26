<div>
    <!-- ARS Button -->
    @if ($existingRanking)
        <div class="relative">
            <div class="flex items-center gap-1">
                <span class="font-bold">{{ $existingRanking->score }}/10</span>
                <button wire:click="toggleFeedbackTooltip"
                    class="text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200 ml-1">
                    <flux:icon name="information-circle" class="size-4" />
                </button>
            </div>

            <!-- Feedback Tooltip -->
            @if ($showFeedbackTooltip)
                <div @click.outside="$wire.toggleFeedbackTooltip"
                    class="absolute z-10 mt-2 w-64 md:w-80 bg-white dark:bg-zinc-800 rounded-lg shadow-lg p-3 border border-zinc-200 dark:border-zinc-700 right-0">
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="font-medium text-zinc-900 dark:text-zinc-100">ARS Feedback</h4>
                        <div class="flex items-center gap-2">
                            <button x-data="{}"
                                x-on:click="
                                    const feedback = `{{ addslashes($existingRanking->feedback) }}`;
                                    const score = `{{ $existingRanking->score }}/10`;
                                    const formattedFeedback = `${score}\n\n${feedback}`;
                         
                                    navigator.clipboard.writeText(formattedFeedback).then(() => {
                                        $wire.dispatch('notify', {
                                            message: 'Feedback copied to clipboard!',
                                            type: 'success'
                                        });
                                    });
                                "
                                class="text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200">
                                <flux:icon name="clipboard-document" class="size-4" />
                            </button>
                            <button wire:click="toggleFeedbackTooltip"
                                class="text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200">
                                <flux:icon name="x-mark" class="size-4" />
                            </button>
                        </div>
                    </div>
                    <p class="text-xs text-zinc-700 dark:text-zinc-300 whitespace-pre-line max-h-40 overflow-y-auto">
                        {{ $existingRanking->feedback }}
                    </p>
                </div>
            @endif
        </div>
    @else
        <flux:button variant="outline" wire:click="rankResume" wire:loading.attr="disabled"
            class="inline-flex items-center gap-1">
            <span wire:loading.remove wire:target="rankResume">
                <flux:icon name="star" class="size-4" />
            </span>
            <span wire:loading wire:target="rankResume" class="animate-spin">
                <flux:icon name="arrow-path" class="size-4" />
            </span>
        </flux:button>
    @endif

    <!-- Ranking Results Modal -->
    <flux:modal name="ranking-results-modal" wire:model="showRankingModal">
        <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
            ARS Ranking Results
        </h2>

        @if ($loadingRanking)
            <div class="flex flex-col items-center justify-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
                <p class="mt-4 text-zinc-600 dark:text-zinc-400">Analyzing resume against job description...</p>
            </div>
        @elseif($rankingResult)
            <div class="space-y-4">
                <div class="flex items-center justify-center">
                    <div class="relative">
                        <div
                            class="flex items-center justify-center w-32 h-32 rounded-full bg-indigo-100 dark:bg-indigo-900">
                            <span
                                class="text-4xl font-bold text-indigo-600 dark:text-indigo-400">{{ $rankingResult->score }}/10</span>
                        </div>
                    </div>
                </div>

                <div class="mt-6">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-sm font-medium text-zinc-900 dark:text-zinc-100">Feedback:</h3>
                        <button x-data="{
                            text: 'Copy'
                        }"
                            x-on:click="
                                const feedback = `{{ addslashes($rankingResult->feedback) }}`;
                                const score = `{{ $rankingResult->score }}/10`;
                                const formattedFeedback = `${score}\n\n${feedback}`;
                         
                                navigator.clipboard.writeText(formattedFeedback).then(() => {
                                    $wire.dispatch('notify', {
                                        message: 'Feedback copied to clipboard!',
                                        type: 'success'
                                    });
                                    text = 'Copied!';
                                    setTimeout(() => {
                                        text = 'Copy';
                                    }, 1000);
                                });
                            "
                            class="text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200 flex items-center">
                            <flux:icon name="clipboard-document" class="size-4" />
                            <span class="text-xs ml-1" x-text="text"></span>
                        </button>
                    </div>
                    <div class="bg-zinc-50 dark:bg-zinc-800 rounded-lg p-3">
                        <p class="text-sm text-zinc-700 dark:text-zinc-300 whitespace-pre-line">
                            {{ $rankingResult->feedback }}</p>
                    </div>
                </div>
            </div>
        @else
            <p class="text-center text-zinc-600 dark:text-zinc-400">No ranking results available.</p>
        @endif

        <flux:button wire:click="closeRankingModal">Close</flux:button>
    </flux:modal>

</div>
