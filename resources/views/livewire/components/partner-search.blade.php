<div class="relative">
    <input
        type="text"
        wire:model.live.debounce.300ms="query"
        wire:keydown.arrow-down.prevent="incrementHighlight"
        wire:keydown.arrow-up.prevent="decrementHighlight"
        wire:keydown.enter.prevent="selectHighlighted"
        wire:keydown.escape="$set('showDropdown', false)"
        placeholder="Search for a partner..."
        class="block w-full rounded-lg border border-zinc-300 px-3 py-2 text-sm placeholder-zinc-400 shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 dark:border-zinc-600 dark:bg-zinc-700 dark:text-zinc-100 dark:placeholder-zinc-500"
    >

    @if ($showDropdown && count($partners) > 0)
        <div class="absolute z-10 mt-1 w-full rounded-md bg-white shadow-lg dark:bg-zinc-800 max-h-60 overflow-auto">
            <ul class="py-1">
                @foreach ($partners as $index => $partner)
                    <li
                        wire:key="{{ $partner->id }}"
                        wire:click="selectPartner('{{ $partner->id }}', '{{ $partner->name }}', '{{ $partner->email }}', '{{ $partner->phone_number }}')"
                        class="{{ $highlightIndex === $index ? 'bg-primary-100 dark:bg-primary-900' : '' }} cursor-pointer px-4 py-2 text-sm hover:bg-primary-50 dark:hover:bg-primary-800"
                    >
                        {{ $partner->name }}
                    </li>
                @endforeach
            </ul>
        </div>
    @elseif ($showDropdown && strlen($query) >= 2 && count($partners) === 0)
        <div class="absolute z-10 mt-1 w-full rounded-md bg-white shadow-lg dark:bg-zinc-800">
            <div class="px-4 py-3 text-sm text-zinc-500 dark:text-zinc-400">
                No partners found. <button wire:click="selectPartner(null, '{{ $query }}', '', '')" class="text-primary-600 hover:underline">Use "{{ $query }}"</button>
            </div>
        </div>
    @endif
</div>
