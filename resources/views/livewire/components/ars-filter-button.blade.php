<div>
    <!-- ARS Filter Button -->
    <div class="flex items-center gap-2">
        @if ($selectedJobDescription)
            <div class="flex items-center gap-1 px-2 py-1 bg-indigo-100 dark:bg-indigo-900 rounded-md">
                <span class="text-sm text-indigo-700 dark:text-indigo-300">{{ $selectedJobDescription->title }}</span>
                <button wire:click="clearJobDescription"
                    class="text-indigo-500 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-200">
                    <flux:icon name="x-mark" class="size-4" />
                </button>
            </div>
        @else
            <div class="flex items-center gap-2">
                <flux:tooltip content="Select Job Description for ARS">
                    <flux:button variant="outline" wire:click="openSelectModal" icon="star">
                        ARS
                    </flux:button>
                </flux:tooltip>
                <flux:tooltip content="Add New Job Description">
                    <flux:button variant="outline" href="{{ route('application-ranking') }}" wire:navigate icon="plus">
                    </flux:button>
                </flux:tooltip>
            </div>
        @endif
    </div>

    <!-- Select Job Description Modal -->
    <flux:modal name="select-jd-modal" wire:model="showSelectModal">
        <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
            Select Job Description for ARS
        </h2>

        <div class="space-y-4">
            <p class="text-sm text-zinc-600 dark:text-zinc-400">
                Select a job description to compare with resumes. You can then use the ARS button in the Actions
                column to rank each resume against this job description.
            </p>

            <!-- Search -->
            <div class="mt-4">
                <flux:input type="search" wire:model.live.debounce.300ms="search"
                    placeholder="Search job descriptions...">
                    <x-slot:prefix>
                        <flux:icon name="magnifying-glass" class="size-5 text-zinc-400" />
                    </x-slot:prefix>
                </flux:input>
            </div>

            <!-- Job Descriptions List -->
            <div class="mt-4 max-h-60 overflow-y-auto">
                @if (count($jobDescriptions) > 0)
                    <div class="space-y-2">
                        @foreach ($jobDescriptions as $jd)
                            <div class="p-3 border border-zinc-200 dark:border-zinc-700 rounded-lg hover:bg-zinc-50 dark:hover:bg-zinc-800 cursor-pointer"
                                wire:click="selectJobDescription({{ $jd->id }})">
                                <div class="font-medium text-zinc-900 dark:text-zinc-100">{{ $jd->title }}</div>
                                <div class="text-xs text-zinc-500 dark:text-zinc-400 mt-1">
                                    {{ \Carbon\Carbon::parse($jd->created_at)->format('d M Y, h:i A') }}
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <p class="text-zinc-500 dark:text-zinc-400">
                            @if ($search)
                                No job descriptions found matching "{{ $search }}".
                            @else
                                No job descriptions found. Please add job descriptions in the ARS section.
                            @endif
                        </p>
                        <div class="mt-4">
                            <flux:button variant="primary" href="{{ route('application-ranking') }}" wire:navigate icon="plus">
                                Add Job Description
                            </flux:button>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <flux:button class="mt-3" variant="outline" wire:click="closeSelectModal">Cancel</flux:button>
    </flux:modal>
</div>
