@php
    $user = auth()->user();
@endphp

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    @include('partials.head')
</head>

<body class="min-h-screen bg-white dark:bg-zinc-800">
    <flux:sidebar sticky stashable
        class="border-r border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-900 transition-all duration-300">
        <div class="flex items-center justify-between mb-2">
            <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />
            <flux:sidebar.desktop-toggle class="hidden! lg:flex!" />
        </div>

        <a href="{{ route('dashboard') }}" class="mr-5 flex items-center space-x-2" wire:navigate>
            <x-app-logo />
        </a>

        <flux:navlist variant="outline">
            <flux:navlist.group class="grid">
                <flux:navlist.item icon="home" :href="route('dashboard')" :current="request()->routeIs('dashboard')"
                    wire:navigate>{{ __('Dashboard') }}
                </flux:navlist.item>

                <flux:navlist.item class="mt-2" icon="magnifying-glass" :href="route('search-candidates')"
                    :current="request()->routeIs('search-candidates')" wire:navigate>{{ __('Search Candidates') }}
                </flux:navlist.item>

                {{-- @if ($user->executive_role === 'Partner')
                    <flux:navlist.item class="mt-2" icon="document-chart-bar" :href="route('resumes')"
                        :current="request()->routeIs('resumes')" wire:navigate>{{ __('Resumes') }}
                    </flux:navlist.item>
                @endif --}}

                @if ($user->role === 'admin')
                    <flux:navlist.item class="mt-2" icon="users" :href="route('executives')"
                        :current="request()->routeIs('executives')" wire:navigate>{{ __('Executives') }}
                    </flux:navlist.item>
                @endif
                @if ($user->role === 'executive')
                    <flux:navlist.item class="mt-2" icon="star" :href="route('application-ranking')"
                        :current="request()->routeIs('application-ranking')" wire:navigate>{{ __('ARS') }}
                    </flux:navlist.item>
                @endif
            </flux:navlist.group>
        </flux:navlist>

        <flux:spacer />

        {{-- Coins Display for Executive --}}
        @if ($user->role === 'executive')
            <div class="px-3 py-4 border-t border-zinc-200 dark:border-zinc-700">
                <div class="flex items-center gap-2 mb-3">
                    <div class="rounded-lg bg-amber-100 p-2 dark:bg-amber-900">
                        <flux:icon name="currency-dollar" class="size-5 text-amber-600 dark:text-amber-400" />
                    </div>
                    <div x-data="{ points: {{ $user->points }} }">
                        <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Available Points</p>
                        <p x-on:points-updated.window="points = $event.detail.points"
                            class="text-lg font-semibold text-zinc-900 dark:text-white" x-text="points"></p>
                    </div>
                </div>

                <flux:modal.trigger name="contact-admin">
                    <flux:button variant="outline" size="sm">
                        <flux:icon name="plus" class="size-4 mr-1" />
                        Request
                    </flux:button>
                </flux:modal.trigger>
            </div>
        @endif

        <!-- Desktop User Menu -->
        <flux:dropdown position="bottom" align="start">
            <flux:profile :name="$user->name" :initials="$user->initials()"
                icon-trailing="chevrons-up-down" />

            <flux:menu class="w-[220px]">
                <flux:menu.radio.group>
                    <div class="p-0 text-sm font-normal">
                        <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                            <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                <span
                                    class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white">
                                    {{ $user->initials() }}
                                </span>
                            </span>

                            <div class="grid flex-1 text-left text-sm leading-tight">
                                <span class="truncate font-semibold">{{ $user->name }}</span>
                                <span class="truncate text-xs">{{ $user->email }}</span>
                            </div>
                        </div>
                    </div>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <flux:menu.radio.group>
                    <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>
                        {{ __('Settings') }}
                    </flux:menu.item>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <form method="POST" action="{{ route('logout') }}" class="w-full">
                    @csrf
                    <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                        {{ __('Log Out') }}
                    </flux:menu.item>
                </form>
            </flux:menu>
        </flux:dropdown>

        {{-- Contact Admin Modal --}}
        <flux:modal name="contact-admin">
            <div class="p-6">
                <h2 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">
                    Contact Admin for Points
                </h2>

                <div class="mt-6 space-y-4">
                    <div class="flex items-center gap-3">
                        <flux:icon name="envelope" class="size-5 text-zinc-500" />
                        <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline">
                            <EMAIL>
                        </a>
                    </div>

                    <div class="flex items-center gap-3">
                        <flux:icon name="globe-alt" class="size-5 text-zinc-500" />
                        <a href="https://www.stargatez.com/contact" target="_blank"
                            class="text-blue-600 hover:underline">
                            www.stargatez.com/contact
                        </a>
                    </div>

                    <div class="flex items-center gap-3">
                        <flux:icon name="phone" class="size-5 text-zinc-500" />
                        <a href="tel:+1234567890" class="text-blue-600 hover:underline">
                            +1 (234) 567-890
                        </a>
                    </div>

                    <div class="flex items-center gap-3">
                        <flux:icon name="chat-bubble-left-ellipsis" class="size-5 text-zinc-500" />
                        <a href="https://wa.me/1234567890" target="_blank" class="text-blue-600 hover:underline">
                            WhatsApp
                        </a>
                    </div>
                </div>

            </div>
        </flux:modal>
    </flux:sidebar>

    <!-- Mobile User Menu -->
    <flux:header class="lg:hidden">
        <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

        <flux:spacer />

        <flux:dropdown position="top" align="end">
            <flux:profile :initials="$user->initials()" icon-trailing="chevron-down" />

            <flux:menu>
                <flux:menu.radio.group>
                    <div class="p-0 text-sm font-normal">
                        <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                            <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                <span
                                    class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white">
                                    {{ $user->initials() }}
                                </span>
                            </span>

                            <div class="grid flex-1 text-left text-sm leading-tight">
                                <span class="truncate font-semibold">{{ $user->name }}</span>
                                <span class="truncate text-xs">{{ $user->email }}</span>
                            </div>
                        </div>
                    </div>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <flux:menu.radio.group>
                    <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>
                        {{ __('Settings') }}
                    </flux:menu.item>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <form method="POST" action="{{ route('logout') }}" class="w-full">
                    @csrf
                    <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle"
                        class="w-full">
                        {{ __('Log Out') }}
                    </flux:menu.item>
                </form>
            </flux:menu>
        </flux:dropdown>
    </flux:header>

    <!-- Dark Mode Toggle -->
    <div class="absolute md:top-5 top-2 md:right-5 right-28 z-50">
        <flux:button x-data x-on:click="$flux.dark = ! $flux.dark" variant="outline" square
            aria-label="Toggle dark mode">
            <flux:icon name="moon" x-show="!$flux.dark" class="size-6" />
            <flux:icon name="sun" x-show="$flux.dark" class="size-6" />
        </flux:button>
    </div>

    {{ $slot }}

    @fluxScripts

    @stack('scripts')
</body>

</html>
