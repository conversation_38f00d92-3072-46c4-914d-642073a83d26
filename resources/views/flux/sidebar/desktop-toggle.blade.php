<flux:button
    :attributes="$attributes->merge([
        'class' => 'shrink-0',
        'variant' => 'subtle',
    ])"
    square
    x-data="{ 
        sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true',
        init() {
            document.body.classList.toggle('sidebar-collapsed', this.sidebarCollapsed);
        }
    }"
    x-on:click="
        sidebarCollapsed = !sidebarCollapsed;
        localStorage.setItem('sidebarCollapsed', sidebarCollapsed);
        document.body.classList.toggle('sidebar-collapsed', sidebarCollapsed);
    "
    data-flux-sidebar-desktop-toggle
    aria-label="{{ __('Toggle sidebar width') }}"
>
    <flux:icon name="chevron-left" class="transition-transform duration-300" x-bind:class="sidebarCollapsed ? 'rotate-180' : ''" />
</flux:button>
