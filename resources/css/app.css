@import 'tailwindcss';
@import '../../vendor/livewire/flux/dist/flux.css';
@import 'easymde/dist/easymde.min.css';

@source "../views";
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../vendor/livewire/flux-pro/stubs/**/*.blade.php';
@source '../../vendor/livewire/flux/stubs/**/*.blade.php';

@custom-variant dark (&:where(.dark, .dark *));

/* City Autocomplete Styles */
.city-autocomplete-wrapper {
    position: relative;
}

.city-autocomplete-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    max-height: 250px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    margin-top: 0.25rem;
}

.dark .city-autocomplete-suggestions {
    background-color: #27272a;
    border-color: #3f3f46;
}

.city-autocomplete-suggestion {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    font-size: 0.875rem;
}

.city-autocomplete-suggestion:hover {
    background-color: #f3f4f6;
}

.dark .city-autocomplete-suggestion:hover {
    background-color: #3f3f46;
}

.city-autocomplete-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.dark .city-autocomplete-loading {
    color: #9ca3af;
}

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --color-zinc-50: #fafafa;
    --color-zinc-100: #f5f5f5;
    --color-zinc-200: #e5e5e5;
    --color-zinc-300: #d4d4d4;
    --color-zinc-400: #a3a3a3;
    --color-zinc-500: #737373;
    --color-zinc-600: #525252;
    --color-zinc-700: #404040;
    --color-zinc-800: #262626;
    --color-zinc-900: #171717;
    --color-zinc-950: #0a0a0a;

    --color-accent: var(--color-neutral-800);
    --color-accent-content: var(--color-neutral-800);
    --color-accent-foreground: var(--color-white);
}

@layer theme {
    .dark {
        --color-accent: var(--color-white);
        --color-accent-content: var(--color-white);
        --color-accent-foreground: var(--color-neutral-800);
    }
}

@layer base {

    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

[data-flux-field] {
    @apply grid gap-2;
}

[data-flux-label] {
    @apply !mb-0 !leading-tight;
}

input:focus[data-flux-control],
textarea:focus[data-flux-control],
select:focus[data-flux-control] {
    @apply outline-hidden ring-2 ring-accent ring-offset-2 ring-offset-accent-foreground;
}

/* \[:where(&)\]:size-4 {
    @apply size-4;
} */

/* Sidebar collapsed state */
.sidebar-collapsed [data-flux-sidebar] {
    width: 4rem !important;
    /* w-16 */
}

.sidebar-collapsed [data-flux-sidebar] .logo {
    width: 100% !important;
    /* w-16 */
    height: fit-content !important;
    /* w-16 */
}

.sidebar-collapsed [data-flux-sidebar] a[wire\:navigate] {
    margin-right: 0;
    justify-content: start;
    margin-bottom: 1rem;
}

.sidebar-collapsed [data-flux-sidebar] a[wire\:navigate]> :not(:first-child) {
    display: none;
}

.sidebar-collapsed [data-flux-sidebar] [data-flux-navlist-item] {
    justify-content: center;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.sidebar-collapsed [data-flux-sidebar] [data-flux-dropdown] [data-content],
.sidebar-collapsed [data-flux-sidebar] [data-flux-profile] [data-content] {
    display: none;
}

.sidebar-collapsed [data-flux-sidebar] [data-flux-navlist-group] button span {
    display: none;
}

/* Style for the desktop sidebar toggle button */
[data-flux-sidebar-desktop-toggle] {
    position: relative;
    margin-right: 0.5rem;
    border-radius: 9999px;
    width: 1.75rem;
    height: 1.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: theme('colors.zinc.100');
    color: theme('colors.zinc.500');
    transition: all 0.3s ease;
}

.dark [data-flux-sidebar-desktop-toggle] {
    background-color: theme('colors.zinc.800');
    color: theme('colors.zinc.400');
}

[data-flux-sidebar-desktop-toggle]:hover {
    background-color: theme('colors.zinc.200');
    color: theme('colors.zinc.700');
}

.dark [data-flux-sidebar-desktop-toggle]:hover {
    background-color: theme('colors.zinc.700');
    color: theme('colors.zinc.300');
}

/* Add custom styles for dark mode support */
.dark .EasyMDEContainer .CodeMirror {
    background-color: theme('colors.zinc.800');
    color: theme('colors.zinc.100');
    border-color: theme('colors.zinc.700');
}

.dark .editor-toolbar {
    background-color: theme('colors.zinc.900');
    border-color: theme('colors.zinc.700');
}

.dark .editor-toolbar button {
    color: theme('colors.zinc.400') !important;
}

.dark .editor-toolbar button.active,
.dark .editor-toolbar button:hover {
    background: theme('colors.zinc.800');
    border-color: theme('colors.zinc.600');
}

.dark .editor-preview {
    background-color: theme('colors.zinc.800');
    color: theme('colors.zinc.100');
}