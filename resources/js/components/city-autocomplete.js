export default function cityAutocomplete() {
    return {
        query: '',
        selectedCity: '',
        suggestions: [],
        isLoading: false,
        showSuggestions: false,
        debounceTimeout: null,

        init() {
            // Initialize with any existing value
            this.query = this.$refs.input.value || '';
            this.selectedCity = this.query;

            // Watch for changes to the query
            this.$watch('query', (value) => {
                if (this.debounceTimeout) {
                    clearTimeout(this.debounceTimeout);
                }

                if (value.length < 2) {
                    this.suggestions = [];
                    this.showSuggestions = false;
                    return;
                }

                this.debounceTimeout = setTimeout(() => {
                    this.searchLocations(value);
                }, 300);
            });

            // Close suggestions when clicking outside
            document.addEventListener('click', (e) => {
                if (!this.$el.contains(e.target)) {
                    this.showSuggestions = false;
                }
            });
        },

        searchLocations(query) {
            this.isLoading = true;
            this.suggestions = [];

            // Search for locations in India
            const url = `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(query)}&format=json&countrycodes=in&addressdetails=1&limit=10`;
            
            fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'Stargatez/1.0'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (Array.isArray(data) && data.length > 0) {
                        // Process the results to extract location information
                        this.suggestions = data.map(item => {
                            // Get the main location name
                            const parts = item.display_name.split(', ');
                            const name = parts[0];
                            
                            // Format a clean address
                            let formattedAddress = '';
                            
                            if (item.address) {
                                const addr = item.address;
                                const addressParts = [];
                                
                                // Add the main components in a logical order
                                if (addr.road || addr.pedestrian || addr.footway) 
                                    addressParts.push(addr.road || addr.pedestrian || addr.footway);
                                if (addr.suburb) addressParts.push(addr.suburb);
                                if (addr.city || addr.town || addr.village) 
                                    addressParts.push(addr.city || addr.town || addr.village);
                                if (addr.county) addressParts.push(addr.county);
                                if (addr.state) addressParts.push(addr.state);
                                
                                formattedAddress = addressParts.join(', ');
                            }
                            
                            // If we couldn't create a good address from components, use the display_name
                            if (!formattedAddress) {
                                // Remove "India" from the end to keep it cleaner
                                formattedAddress = item.display_name.replace(/, India$/, '');
                            }
                            
                            return {
                                name: formattedAddress,
                                fullName: formattedAddress,
                                originalName: name,
                                displayName: item.display_name
                            };
                        });

                        // Remove duplicates
                        this.suggestions = this.suggestions
                            .filter((item, index, self) => 
                                index === self.findIndex((t) => t.name === item.name)
                            );

                        this.showSuggestions = this.suggestions.length > 0;
                    } else {
                        this.suggestions = [];
                        this.showSuggestions = false;
                    }
                    this.isLoading = false;
                })
                .catch(error => {
                    console.error('Error fetching location data:', error);
                    this.isLoading = false;
                    this.suggestions = [];
                    this.showSuggestions = false;
                });
        },

        selectCity(city) {
            this.query = city.name;
            this.selectedCity = city.name;
            this.showSuggestions = false;

            // Update the Livewire property directly
            const inputName = this.$refs.input.getAttribute('wire:model') ||
                this.$refs.input.getAttribute('wire:model.defer');
            if (inputName) {
                this.$wire.set(inputName, city.name);
            }

            // Dispatch an event that can be listened to
            this.$dispatch('city-selected', { city: city.name });
        }
    };
}
