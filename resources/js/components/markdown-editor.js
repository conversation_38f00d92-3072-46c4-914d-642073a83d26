import EasyMDE from 'easymde';

export default function markdownEditor() {
    return {
        editor: null,
        content: '',
        init() {
            // Get the initial value from the textarea
            const initialValue = this.$refs.textarea.value || '';

            this.editor = new EasyMDE({
                element: this.$refs.textarea,
                spellChecker: false,
                autosave: {
                    enabled: true,
                    uniqueId: 'resume_content',
                },
                placeholder: 'Enter resume content...',
                status: ['lines', 'words', 'cursor'],
                toolbar: [
                    'bold', 'italic', 'heading', '|',
                    'quote', 'unordered-list', 'ordered-list', '|',
                    'link', 'table', '|',
                    'preview', 'side-by-side', 'fullscreen', '|',
                    'guide'
                ],
                initialValue: initialValue // Set the initial value here
            });

            // Sync with Livewire
            this.editor.codemirror.on('change', () => {
                this.$wire.set('resume_content', this.editor.value());
            });


            // Listen for Livewire updates
            this.$watch('content', (value) => {
                if (value && this.editor.value() !== value) {
                    this.editor.value(value);
                }
            });
        }
    }
}