<?php

use App\Http\Controllers\Api\ResumeParserController;
use Illuminate\Support\Facades\Route;

Route::controller(ResumeParserController::class)->group(function () {
    Route::post('parse-resume', 'parse')->name('api.parse-resume')->middleware('throttle:60,1');
    Route::post('save-resume', 'saveResume')->middleware('throttle:60,1');
    Route::post('cities', 'getCities')->middleware('throttle:60,1');
    Route::post('partners', 'getPartners')->middleware('throttle:60,1');
    Route::post('validate-email-phone', 'validateEmailPhone')->middleware('throttle:60,1');
});
