<?php

use App\Http\Controllers\Api\ResumeParserController;
use App\Http\Controllers\Api\ResumeAtsController;
use Illuminate\Support\Facades\Route;

Route::controller(ResumeParserController::class)->group(function () {
    Route::post('parse-resume', 'parse')->name('api.parse-resume')->middleware('throttle:60,1');
    Route::post('save-resume', 'saveResume')->middleware('throttle:60,1');
    Route::post('cities', 'getCities')->middleware('throttle:60,1');
    Route::post('partners', 'getPartners')->middleware('throttle:60,1');
    Route::post('validate-email-phone', 'validateEmailPhone')->middleware('throttle:60,1');
});

// ATS Resume Routes
Route::controller(ResumeAtsController::class)->group(function () {
    Route::post('save-resume-ats', 'saveResumeWithAts')->name('api.save-resume-ats')->middleware('throttle:60,1');
    Route::post('parse-ats-score', 'parseAtsScore')->name('api.parse-ats-score')->middleware('throttle:60,1');
});
