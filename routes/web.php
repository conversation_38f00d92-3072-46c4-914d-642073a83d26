<?php

use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\AdminPartnerMiddleware;
use App\Livewire\Pages\ApplicationRanking;
use App\Livewire\Pages\Executives;
use App\Livewire\Pages\CreateResume;
use App\Livewire\Pages\Dashboard;
use App\Livewire\Pages\Resumes;
use App\Livewire\Pages\TrashResumes;
use App\Livewire\SearchCandidates;
use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Route;

Route::get('storage:link', function () {
    Artisan::call('storage:link');
})->name('storage.link');

Route::get('dashboard', Dashboard::class)
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::get('search-candidates', SearchCandidates::class)->name('search-candidates');
    Route::get('resumes', Resumes::class)->name('resumes');
    Route::get('application-ranking', ApplicationRanking::class)->name('application-ranking');

    Route::get('resumes/create', CreateResume::class)->name('resumes.create')->middleware(AdminPartnerMiddleware::class);
    Route::middleware(AdminMiddleware::class)->group(function () {
        Route::get('resumes/{id}/edit', CreateResume::class)->name('resumes.edit');
        Route::get('resumes/trash', TrashResumes::class)->name('resumes.trash');

        Route::get('executives', Executives::class)->name('executives');
    });

    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');
});

require __DIR__ . '/auth.php';
