<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('resume_unlocks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete()->noActionOnUpdate();
            $table->foreignId('resume_id')->constrained()->cascadeOnDelete()->noActionOnUpdate();
            $table->boolean('viewed')->default(false);
            $table->boolean('downloaded')->default(false);
            $table->timestamps();

            $table->unique(['user_id', 'resume_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('resume_unlocks');
    }
};
