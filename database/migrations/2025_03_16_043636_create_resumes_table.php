<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('resumes', function (Blueprint $table) {
            $table->id();
            $table->string('candidate_name')->nullable();
            $table->string('country_code')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('email')->nullable();
            $table->longText('skills')->nullable();
            $table->string('total_experience_years')->nullable();
            $table->string('total_experience_months')->nullable();
            $table->string('current_company')->nullable();
            $table->string('designation')->nullable();
            $table->string('current_location')->nullable();
            $table->string('qualification')->nullable();
            $table->string('university')->nullable();
            $table->string('industry')->nullable();
            $table->string('preferred_location')->nullable();
            $table->string('current_salary_lacs')->nullable();
            $table->string('current_salary_thousands')->nullable();
            $table->string('expected_salary_lacs')->nullable();
            $table->string('expected_salary_thousands')->nullable();
            $table->string('notice_period')->nullable();
            $table->string('resume_file')->nullable();
            $table->string('resume_file_name')->nullable();
            $table->string('partner_name')->nullable();
            $table->boolean('is_archived')->default(false);
            $table->longText('resume_content')->nullable();
            $table->string('home_town')->nullable();
            $table->string('relevant_experience_years')->nullable();
            $table->string('relevant_experience_months')->nullable();
            $table->string('comments')->nullable();
            $table->string('submitted_from')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('resumes');
    }
};
