<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('resume_ats_scores', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->unsignedBigInteger('resume_id');
            $table->string('resume_score')->nullable();
            $table->string('ats_friendly_status')->nullable();
            $table->longText('resume_feedback')->nullable();
            $table->longText('suggestions_for_improvement')->nullable();
            $table->enum('status', ['pending', 'parsed'])->default('pending');
            $table->timestamps();

            $table->foreign('resume_id')->references('id')->on('resumes')->onDelete('cascade');
            $table->index('resume_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('resume_ats_scores');
    }
};
