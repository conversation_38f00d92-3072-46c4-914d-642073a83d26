<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_descriptions', function (Blueprint $table) {
            // Rename content to description
            $table->renameColumn('content', 'description');

            // Drop columns that are no longer needed
            $table->dropColumn(['file_path', 'original_filename', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_descriptions', function (Blueprint $table) {
            // Rename description back to content
            $table->renameColumn('description', 'content');

            // Add back the columns that were dropped
            $table->string('file_path')->nullable();
            $table->string('original_filename')->nullable();
            $table->boolean('is_active')->default(true);
        });
    }
};
