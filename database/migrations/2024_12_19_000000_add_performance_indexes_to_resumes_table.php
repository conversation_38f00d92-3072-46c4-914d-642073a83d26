<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('resumes', function (Blueprint $table) {
            // Add indexes for frequently searched/filtered columns
            
            // Single column indexes for search fields
            $table->index('candidate_name', 'idx_resumes_candidate_name');
            $table->index('email', 'idx_resumes_email');
            $table->index('phone_number', 'idx_resumes_phone_number');
            $table->index('current_location', 'idx_resumes_current_location');
            $table->index('current_company', 'idx_resumes_current_company');
            $table->index('designation', 'idx_resumes_designation');
            $table->index('partner_name', 'idx_resumes_partner_name');
            $table->index('qualification', 'idx_resumes_qualification');
            
            // Indexes for filter fields
            $table->index('is_archived', 'idx_resumes_is_archived');
            $table->index('submitted_from', 'idx_resumes_submitted_from');
            $table->index('total_experience_years', 'idx_resumes_total_exp_years');
            $table->index('expected_salary_lacs', 'idx_resumes_expected_salary_lacs');
            $table->index('current_salary_lacs', 'idx_resumes_current_salary_lacs');
            $table->index('notice_period', 'idx_resumes_notice_period');
            
            // Date indexes for date range filters
            $table->index('created_at', 'idx_resumes_created_at');
            $table->index('updated_at', 'idx_resumes_updated_at');
            
            // Composite indexes for common filter combinations
            $table->index(['is_archived', 'created_at'], 'idx_resumes_archived_created');
            $table->index(['submitted_from', 'is_archived'], 'idx_resumes_submitted_archived');
            $table->index(['total_experience_years', 'is_archived'], 'idx_resumes_exp_archived');
            $table->index(['expected_salary_lacs', 'is_archived'], 'idx_resumes_salary_archived');
            
            // Indexes for duplicate detection (performance optimization)
            $table->index(['email', 'deleted_at'], 'idx_resumes_email_deleted');
            $table->index(['phone_number', 'deleted_at'], 'idx_resumes_phone_deleted');
            
            // Soft delete index
            $table->index('deleted_at', 'idx_resumes_deleted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('resumes', function (Blueprint $table) {
            // Drop all the indexes we created
            $table->dropIndex('idx_resumes_candidate_name');
            $table->dropIndex('idx_resumes_email');
            $table->dropIndex('idx_resumes_phone_number');
            $table->dropIndex('idx_resumes_current_location');
            $table->dropIndex('idx_resumes_current_company');
            $table->dropIndex('idx_resumes_designation');
            $table->dropIndex('idx_resumes_partner_name');
            $table->dropIndex('idx_resumes_qualification');
            $table->dropIndex('idx_resumes_is_archived');
            $table->dropIndex('idx_resumes_submitted_from');
            $table->dropIndex('idx_resumes_total_exp_years');
            $table->dropIndex('idx_resumes_expected_salary_lacs');
            $table->dropIndex('idx_resumes_current_salary_lacs');
            $table->dropIndex('idx_resumes_notice_period');
            $table->dropIndex('idx_resumes_created_at');
            $table->dropIndex('idx_resumes_updated_at');
            $table->dropIndex('idx_resumes_archived_created');
            $table->dropIndex('idx_resumes_submitted_archived');
            $table->dropIndex('idx_resumes_exp_archived');
            $table->dropIndex('idx_resumes_salary_archived');
            $table->dropIndex('idx_resumes_email_deleted');
            $table->dropIndex('idx_resumes_phone_deleted');
            $table->dropIndex('idx_resumes_deleted_at');
        });
    }
};
