<?php

namespace Database\Seeders;

use App\Models\City;
use Illuminate\Database\Seeder;

class CitiesSeeder extends Seeder
{
      /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // // List of major Indian cities
        // $cities = [
         
        // ];

        // List of Indian states
        $states = [
            'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 
            'Chhattisgarh', 'Goa', 'Gujarat', 'Haryana', 
            'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala', 
            'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 
            'Mizoram', 'Nagaland', 'Odisha', 'Punjab', 
            'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana', 
            'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal'
        ];

        // // Insert cities into the database
        // foreach ($cities as $city) {
        //     City::firstOrCreate(['name' => $city]);
        // }

        // Insert states into the database
        foreach ($states as $state) {
            City::firstOrCreate(['name' => $state]);
        }
    }
}
