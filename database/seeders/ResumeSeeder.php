<?php

namespace Database\Seeders;

use App\Models\Resume;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ResumeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Resume::create(
            [
                'candidate_name' => '<PERSON>',
                'phone_number' => '1234567890',
                'email' => '<EMAIL>',
                'skills' => ['PHP', 'Laravel', 'MySQL'],
                'total_experience' => '5 years',
                'relevant_experience' => '3 years',
                'current_org' => 'ABC Company',
                'current_location' => 'New York',
                'current_ctc' => '50000',
                'expected_ctc' => '60000',
                'notice_period' => '30 days',
                'resume_file' => 'resume.pdf',
                'resume_file_name' => 'resume.pdf',
            ],
            [
                'candidate_name' => '<PERSON>',
                'phone_number' => '9876543210',
                'email' => '<EMAIL>',
                'skills' => ['JavaScript', 'React', 'Node.js'],
                'total_experience' => '4 years',
                'relevant_experience' => '2 years',
                'current_org' => 'XYZ Company',
                'current_location' => 'San Francisco',
                'current_ctc' => '45000',
                'expected_ctc' => '55000',
                'notice_period' => '15 days',
                'resume_file' => 'resume.pdf',
                'resume_file_name' => 'resume.pdf',
            ]
        );
    }
}
