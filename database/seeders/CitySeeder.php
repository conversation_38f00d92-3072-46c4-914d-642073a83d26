<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\City;
use Maatwebsite\Excel\Facades\Excel;

class CitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */

    public function run()
    {
        $path = public_path('cities.xlsx');

        $collection = Excel::toCollection(null, $path)->first(); // Get the first sheet

        foreach ($collection as $index => $row) {
            if($index == 0) continue;
            if (isset($row[0]) && !empty(trim($row[0]))) {
                City::firstOrCreate(['name' => trim($row[0])]);
            }
        }
    }
}
