# ATS Resume API Documentation

This document describes the new ATS (Applicant Tracking System) scoring functionality APIs.

## Overview

The ATS Resume API provides two main endpoints:
1. **Save Resume with ATS** - Upload and save resume data, create ATS score record
2. **Parse ATS Score** - Analyze resume using ChatGPT and generate ATS compatibility score

## API Endpoints

### 1. Save Resume with ATS

**Endpoint:** `POST /api/save-resume-ats`

**Description:** Uploads a resume file, extracts text content, saves resume data to database, and creates an ATS score record with UUID.

**Request Parameters:**

**Required Fields:**
- `resume_file` (file) - PDF, DOC, or DOCX file
- `candidate_name` (string, max 255)
- `phone_number` (string, max 40)
- `email` (email, max 255)
- `skills` (string or array) - Comma-separated or array format

**Optional Fields from File Parsing:**
- `total_experience_years` (max 100)
- `total_experience_months` (max 100)
- `current_company` (string, max 255)
- `designation` (string, max 255)
- `current_location` (string, max 255)
- `qualification` (string, max 255)

**Optional Fields from Payload:**
- `current_salary_lacs` (string)
- `current_salary_thousands` (string)
- `notice_period` (max 100)
- `partner_name` (string, max 255)
- `comments` (string, max 1000)
- `submitted_from` (string, max 255)
- `partner_id` (exists in users table)
- `partner_email` (email, max 255)
- `partner_phone_number` (string, max 40)

**Response:**
```json
{
    "success": true,
    "message": "Resume saved successfully",
    "resume_id": 123,
    "ats_uuid": "550e8400-e29b-41d4-a716-************",
    "data": {
        "candidate_name": "John Doe",
        "email": "<EMAIL>",
        "phone_number": "+1234567890"
    }
}
```

**Error Response:**
```json
{
    "error": "Validation error message"
}
```

### 2. Parse ATS Score

**Endpoint:** `POST /api/parse-ats-score`

**Description:** Analyzes a resume using ChatGPT to generate ATS compatibility score and feedback. Returns cached data if already parsed.

**Request Parameters:**
- `uuid` (required, UUID) - The ATS score UUID returned from save-resume-ats

**Response (First Time Parsing):**
```json
{
    "success": true,
    "message": "ATS score parsed successfully",
    "data": {
        "uuid": "550e8400-e29b-41d4-a716-************",
        "resume_score": 85,
        "ats_friendly_status": "good",
        "resume_feedback": "The resume shows good ATS compatibility with clear structure and relevant keywords. Contact information is complete and properly formatted. Skills section is well-organized with relevant technical keywords.",
        "suggestions_for_improvement": "1. Add more industry-specific keywords in the experience section. 2. Use standard section headings like 'Professional Experience' instead of custom titles. 3. Include more quantifiable achievements with numbers and percentages."
    }
}
```

**Response (Already Parsed):**
```json
{
    "success": true,
    "message": "ATS score already parsed",
    "data": {
        "uuid": "550e8400-e29b-41d4-a716-************",
        "resume_score": 85,
        "ats_friendly_status": "good",
        "resume_feedback": "...",
        "suggestions_for_improvement": "..."
    }
}
```

**Error Response:**
```json
{
    "error": "ATS score record not found"
}
```

## ATS Scoring Criteria

### Score Range: 1-100
- **90-100**: Excellent ATS compatibility
- **70-89**: Good ATS compatibility  
- **50-69**: Fair ATS compatibility
- **Below 50**: Poor ATS compatibility

### ATS Friendly Status Values:
- `excellent` - Score 90-100
- `good` - Score 70-89
- `fair` - Score 50-69
- `poor` - Score below 50

## Database Schema

### ResumeAtsScore Model
```php
- id (UUID, Primary Key)
- resume_id (Foreign Key to resumes table)
- resume_score (Integer, 1-100)
- ats_friendly_status (Enum: excellent, good, fair, poor)
- resume_feedback (Long Text)
- suggestions_for_improvement (Long Text)
- created_at (Timestamp)
- updated_at (Timestamp)
```

## Usage Flow

1. **Upload Resume**: Call `save-resume-ats` with resume file and data
2. **Get UUID**: Extract the `ats_uuid` from the response
3. **Parse ATS Score**: Call `parse-ats-score` with the UUID
4. **Display Results**: Show the ATS score, status, feedback, and suggestions

## Error Handling

- **400**: Validation errors (missing fields, invalid file format)
- **404**: ATS score record not found
- **500**: Server errors (file processing, ChatGPT API issues)

## Rate Limiting

Both endpoints are rate-limited to 60 requests per minute per IP address.

## ChatGPT Integration

The ATS parsing uses OpenAI's GPT-4 Turbo model to analyze:
- Keyword optimization and relevance
- Format and structure compatibility
- Contact information completeness
- Skills presentation
- Experience description clarity
- Education formatting
- Overall ATS readability

## Configuration

Ensure the following environment variable is set:
```
OPENAI_API_KEY=your_openai_api_key_here
```
